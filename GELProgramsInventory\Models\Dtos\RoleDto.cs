namespace GELProgramsInventory.Models.Dtos;

public class RoleDto
{
    public int RoleId { get; set; }
    public string? RoleTitle { get; set; }
    public bool RoleIsActive { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime ModifiedDate { get; set; }
    public string? CreatedBy { get; set; }
    public string? ModifiedBy { get; set; }
    public string Status => RoleIsActive ? "Active" : "Inactive";
    public List<int> AssignedMenuIds { get; set; } = new();
    public List<MenuDto> AssignedMenus { get; set; } = new();
}

public class RoleMenuDto
{
    public int RoleId { get; set; }
    public int MenuId { get; set; }
    public DateTime CreatedDate { get; set; }
    public string? CreatedBy { get; set; }
    public string? RoleTitle { get; set; }
    public string? MenuTitle { get; set; }
}
