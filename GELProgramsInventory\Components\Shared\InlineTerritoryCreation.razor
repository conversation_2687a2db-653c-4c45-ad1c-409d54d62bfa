@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@inject TerritoryService TerritoryService
@rendermode InteractiveServer

<SfDialog Width="500px" IsModal="true" @bind-Visible="IsVisible" CssClass="territory-dialog">
    <DialogTemplates>
        <Header>
            <div class="dialog-header">
                <h5 class="dialog-title">Add New Territory</h5>
            </div>
        </Header>
        <Content>
            <EditForm Model="NewTerritory" OnValidSubmit="SaveTerritory" FormName="TerritoryForm">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="form-group">
                    <label for="territoryName" class="required">Territory Name</label>
                    <SfTextBox ID="territoryName" @bind-Value="NewTerritory.Name" Placeholder="Enter territory name"></SfTextBox>
                    <ValidationMessage For="@(() => NewTerritory.Name)" />
                </div>

                <div class="form-group">
                    <label for="territoryDescription">Description</label>
                    <SfTextBox ID="territoryDescription" @bind-Value="NewTerritory.Description" Placeholder="Enter description" Multiline="true" Rows="3"></SfTextBox>
                </div>

                <div class="form-group">
                    <div class="e-checkbox-wrapper">
                        <SfCheckBox @bind-Checked="NewTerritory.IsActive" Label="Active"></SfCheckBox>
                    </div>
                </div>

                <div class="dialog-footer">
                    <SfButton Type="submit" CssClass="e-primary">Save Territory</SfButton>
                    <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .territory-dialog .dialog-header {
        padding: 10px 15px;
    }

    .territory-dialog .dialog-title {
        margin: 0;
        font-size: 1.25rem;
    }

    .territory-dialog .form-group {
        margin-bottom: 1.5rem;
    }

    .territory-dialog label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .territory-dialog label.required::after {
        content: " *";
        color: #dc3545;
    }

    .territory-dialog .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 1.5rem;
    }
</style>

@code {
    [Parameter]
    public EventCallback<TerritoryDto> OnTerritoryCreated { get; set; }

    private TerritoryDto NewTerritory { get; set; } = new TerritoryDto { IsActive = true };
    private bool IsVisible { get; set; }

    public async Task ShowDialog()
    {
        NewTerritory = new TerritoryDto { IsActive = true };
        IsVisible = true;
        await InvokeAsync(StateHasChanged);
    }

    private void CloseDialog()
    {
        IsVisible = false;
    }

    private async Task SaveTerritory()
    {
        try
        {
            var createdTerritory = await TerritoryService.CreateTerritoryAsync(NewTerritory);
            IsVisible = false;
            await OnTerritoryCreated.InvokeAsync(createdTerritory);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating territory: {ex.Message}");
        }
    }
}