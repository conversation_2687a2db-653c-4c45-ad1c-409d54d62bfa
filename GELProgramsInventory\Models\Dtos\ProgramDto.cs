using System.ComponentModel.DataAnnotations;

namespace GELProgramsInventory.Models.Dtos;

public class ProgramDto
{
    public int ProgramId { get; set; }

    // New naming structure
    [Required(ErrorMessage = "Primary name is required")]
    [StringLength(300, ErrorMessage = "Primary name cannot exceed 300 characters")]
    public string? PrimaryName { get; set; }

    [StringLength(300, ErrorMessage = "Secondary name cannot exceed 300 characters")]
    public string? SecondaryName1 { get; set; }

    [StringLength(300, ErrorMessage = "Secondary name cannot exceed 300 characters")]
    public string? SecondaryName2 { get; set; }

    [StringLength(300, ErrorMessage = "Secondary name cannot exceed 300 characters")]
    public string? SecondaryName3 { get; set; }

    [StringLength(300, ErrorMessage = "Secondary name cannot exceed 300 characters")]
    public string? SecondaryName4 { get; set; }

    // New fields
    [Range(0, int.MaxValue, ErrorMessage = "Total episodes must be 0 or greater")]
    public int? TotalEpisodes { get; set; }

    [StringLength(1000, ErrorMessage = "Remarks cannot exceed 1000 characters")]
    public string? Remarks { get; set; }
    
    [StringLength(1000, ErrorMessage = "Syndication delivery remarks cannot exceed 1000 characters")]
    public string? SyndicationDeliveryRemarks { get; set; }
    
    [Range(0, int.MaxValue, ErrorMessage = "Duration must be 0 or greater")]
    public int? Duration { get; set; }

    // Keep existing fields for backward compatibility during transition
    public string? ProgramName { get; set; }
    public int? GenreId { get; set; }
    public string? GenreName { get; set; }
    public int? ProductionHouseId { get; set; }
    public string? ProductionHouseName { get; set; }
    public int? OnAirFormatId { get; set; }
    public string? OnAirFormatName { get; set; }

    public string? DriveSerialNumber { get; set; }
    public string? YearOfLaunch { get; set; }
    public bool? IsActive { get; set; }
    public string Status => IsActive == true ? "Active" : "Inactive";
    public List<SyndicationDto> Syndication { get; set; } = new List<SyndicationDto>();
    // Remove syndication-related fields as they will be in SyndicationDto
    // public int? ClientId { get; set; }
    // public string? ClientName { get; set; }
    // public int? SyndicationFormatId { get; set; }
    // public string? SyndicationFormatName { get; set; }
    // public bool? IsSyndication { get; set; }
    // public string? Remark { get; set; }
}