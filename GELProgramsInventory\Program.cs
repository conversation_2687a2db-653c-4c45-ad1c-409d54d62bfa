using GELProgramsInventory.Components;
using GELProgramsInventory.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor;

var builder = WebApplication.CreateBuilder(args);
Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(
    "Mzk0NDgxOEAzMzMwMmUzMDJlMzAzYjMzMzAzYk8yYWFoVlJxSzRsSm4xcjhTZEJtL1M2bVM0TXcyT3N4bkw3N2w2S0ZSZjg9");
// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();
builder.Services.AddFluentUIComponents();
builder.Services.AddSyncfusionBlazor();


// Add Entity Framework
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));
builder.Services.AddScoped<GELProgramsInventory.Services.ClientService>();
builder.Services.AddScoped<GELProgramsInventory.Services.ProductionHouseService>();
builder.Services.AddScoped<GELProgramsInventory.Services.ProgramService>();
builder.Services.AddScoped<GELProgramsInventory.Services.GenreService>();
builder.Services.AddScoped<GELProgramsInventory.Services.OnAirFormatService>();
builder.Services.AddScoped<GELProgramsInventory.Services.SyndicationFormatService>();
builder.Services.AddScoped<GELProgramsInventory.Services.LanguageService>();
builder.Services.AddScoped<GELProgramsInventory.Services.SyndicationService>();
builder.Services.AddScoped<GELProgramsInventory.Services.CurrencyService>();
builder.Services.AddScoped<GELProgramsInventory.Services.TerritoryService>();
builder.Services.AddScoped<GELProgramsInventory.Services.ContractDocumentService>();

var app = builder.Build();

// Note: Uncomment the following lines after ensuring the Languages table exists in your database
// Seed default languages
//using (var scope = app.Services.CreateScope())
//{
//    var languageService = scope.ServiceProvider.GetRequiredService<GELProgramsInventory.Services.LanguageService>();
//    await languageService.SeedDefaultLanguagesAsync();
//}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
