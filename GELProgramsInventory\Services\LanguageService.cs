using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;

namespace GELProgramsInventory.Services;

public class LanguageService(ApplicationDbContext context)
{
    public Task<List<LanguageDto>> GetLanguagesAsync()
    {
        var res = context.Languages
            .Select(l => new LanguageDto
            {
                LanguageId = l.LanguageId,
                Name = l.Title,
                Title = l.Title
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<LanguageDto?> GetLanguageByIdAsync(int id)
    {
        var language = context.Languages.Find(id);
        if (language == null)
            return Task.FromResult<LanguageDto?>(null);

        var res = new LanguageDto
        {
            LanguageId = language.LanguageId,
            Name = language.Title,
            Title = language.Title
        };
        return Task.FromResult<LanguageDto?>(res);
    }

    public Task<Language> CreateLanguageAsync(LanguageDto languageDto)
    {
        var language = new Language
        {
            Title = languageDto.Title
        };

        context.Languages.Add(language);
        context.SaveChanges();
        return Task.FromResult(language);
    }

    public Task UpdateLanguageAsync(LanguageDto languageDto)
    {
        var language = context.Languages.Find(languageDto.LanguageId);
        if (language != null)
        {
            language.Title = languageDto.Title;
            context.SaveChanges();
        }
        return Task.CompletedTask;
    }

    public Task DeleteLanguageAsync(int id)
    {
        var language = context.Languages.Find(id);
        if (language != null)
        {
            context.Languages.Remove(language);
            context.SaveChanges();
        }
        return Task.CompletedTask;
    }

    public Task SeedDefaultLanguagesAsync()
    {
        var existingLanguages = context.Languages.ToList();
        
        var defaultLanguages = new List<string> { "Urdu", "English", "Turkish" };
        
        foreach (var langName in defaultLanguages)
        {
            if (!existingLanguages.Any(l => l.Title.Equals(langName, StringComparison.OrdinalIgnoreCase)))
            {
                context.Languages.Add(new Language { Title = langName });
            }
        }
        
        context.SaveChanges();
        return Task.CompletedTask;
    }
}
