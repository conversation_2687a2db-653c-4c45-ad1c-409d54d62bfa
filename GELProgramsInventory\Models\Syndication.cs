﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace GELProgramsInventory.Models;

public partial class Syndication
{
    public int Id { get; set; }

    public int? ProgramId { get; set; }

    public int? ClientId { get; set; }

    public int? SyndicationFormatId { get; set; }

    public string Remark { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? LanguageId { get; set; }

    public DateTime? ContractStartDate { get; set; }

    public DateTime? ContractEndDate { get; set; }

    public int? TerritoryId { get; set; }

    public int? EpisodeFrom { get; set; }

    public int? EpisodeTo { get; set; }

    public decimal? PricePerEpisode { get; set; }

    public int? CurrencyId { get; set; }

    public int? OnAirFormatId { get; set; }

    public virtual Client Client { get; set; }

    public virtual ICollection<ContractDocument> ContractDocuments { get; set; } = new List<ContractDocument>();

    public virtual Currency Currency { get; set; }

    public virtual Language Language { get; set; }

    public virtual OnAirFormat OnAirFormat { get; set; }

    public virtual Program Program { get; set; }

    public virtual SyndicationFormat SyndicationFormat { get; set; }

    public virtual Territory Territory { get; set; }
}