using System.ComponentModel.DataAnnotations;

namespace GELProgramsInventory.Models.Dtos;

public class CurrencyDto
{
    public int CurrencyId { get; set; }
    
    [Required(ErrorMessage = "Currency code is required")]
    public string Code { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Currency name is required")]
    public string Name { get; set; } = string.Empty;
    
    public string? Symbol { get; set; }
    
    public bool? IsActive { get; set; }
}