/* RTL Support for Urdu Language */

/* Apply RTL when html has dir="rtl" attribute */
html[dir="rtl"] {
    direction: rtl;
}

html[dir="rtl"] body {
    text-align: right;
}

/* Navigation Menu RTL */
html[dir="rtl"] .navmenu {
    right: 0;
    left: auto;
}

html[dir="rtl"] .sitenav {
    text-align: right;
}

/* Breadcrumb RTL */
html[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "<";
    padding: 0 8px;
}

html[dir="rtl"] .breadcrumb {
    flex-direction: row-reverse;
}

/* Form RTL */
html[dir="rtl"] .form-group {
    text-align: right;
}

html[dir="rtl"] .form-group label {
    text-align: right;
}

html[dir="rtl"] .form-actions {
    flex-direction: row-reverse;
}

/* Grid and Tree Grid RTL */
html[dir="rtl"] .action-buttons {
    flex-direction: row-reverse;
}

html[dir="rtl"] .toolbar-section {
    justify-content: flex-end;
}

/* Dropdown RTL */
html[dir="rtl"] .dropdown-with-add {
    flex-direction: row-reverse;
}

/* Dialog RTL */
html[dir="rtl"] .e-dialog .e-dlg-header {
    text-align: right;
}

html[dir="rtl"] .e-dialog .e-dlg-content {
    text-align: right;
}

/* Syncfusion Components RTL Support */
html[dir="rtl"] .e-textbox,
html[dir="rtl"] .e-input-group,
html[dir="rtl"] .e-dropdownlist,
html[dir="rtl"] .e-numerictextbox {
    direction: rtl;
    text-align: right;
}

html[dir="rtl"] .e-float-text {
    right: 8px;
    left: auto;
}

html[dir="rtl"] .e-input-group .e-input {
    text-align: right;
}

/* Button RTL */
html[dir="rtl"] .e-btn .e-btn-icon {
    margin-left: 4px;
    margin-right: 0;
}

/* Tree Grid RTL */
html[dir="rtl"] .e-treegrid .e-rowcell {
    text-align: right;
}

html[dir="rtl"] .e-treegrid .e-treegridexpand,
html[dir="rtl"] .e-treegrid .e-treegridcollapse {
    float: right;
    margin-left: 4px;
    margin-right: 0;
}

/* Grid RTL */
html[dir="rtl"] .e-grid .e-rowcell {
    text-align: right;
}

html[dir="rtl"] .e-grid .e-headertext {
    text-align: right;
}

/* Language-specific font support */
html[dir="rtl"] {
    font-family: 'Segoe UI', 'Noto Sans Arabic', 'Noto Sans Urdu', Tahoma, Arial, sans-serif;
}

/* Responsive RTL */
@media (max-width: 768px) {
    html[dir="rtl"] .form-row {
        direction: rtl;
    }
    
    html[dir="rtl"] .dashboard-title {
        flex-direction: column-reverse;
    }
    
    html[dir="rtl"] .welcome-content ul {
        text-align: right;
    }
}

/* Utility classes for RTL */
.rtl-text {
    direction: rtl;
    text-align: right;
}

.ltr-text {
    direction: ltr;
    text-align: left;
}

/* Language switcher styles */
.language-switcher {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

html[dir="rtl"] .language-switcher {
    right: auto;
    left: 10px;
}

.language-switcher select {
    padding: 5px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
}
