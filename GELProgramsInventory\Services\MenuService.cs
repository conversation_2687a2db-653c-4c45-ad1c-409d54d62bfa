using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class MenuService(ApplicationDbContext context)
{
    public Task<List<MenuDto>> GetMenusAsync()
    {
        var menus = context.Menus
            .Include(m => m.RoleMenus)
            .ThenInclude(rm => rm.Role)
            .Select(m => new MenuDto
            {
                MenuId = m.MenuId,
                MemuTitle = m.MemuTitle,
                MenuLink = m.MenuLink,
                SortOrder = m.SortOrder,
                CreatedDate = m.CreatedDate,
                ModifiedDate = m.ModifiedDate,
                CreatedBy = m.CreatedBy,
                ModifiedBy = m.ModifiedBy,
                Module = m.Module,
                Icon = m.Icon,
                AssignedRoleIds = m.RoleMenus.Select(rm => rm.RoleId).ToList(),
                AssignedRoles = m.RoleMenus.Select(rm => new RoleDto
                {
                    RoleId = rm.Role.RoleId,
                    RoleTitle = rm.Role.RoleTitle,
                    RoleIsActive = rm.Role.RoleIsActive
                }).ToList()
            })
            .OrderBy(m => m.Module)
            .ThenBy(m => m.SortOrder)
            .ThenBy(m => m.MemuTitle)
            .ToList();

        return Task.FromResult(menus);
    }

    public Task<MenuDto?> GetMenuByIdAsync(int id)
    {
        var menu = context.Menus
            .Include(m => m.RoleMenus)
            .ThenInclude(rm => rm.Role)
            .Where(m => m.MenuId == id)
            .Select(m => new MenuDto
            {
                MenuId = m.MenuId,
                MemuTitle = m.MemuTitle,
                MenuLink = m.MenuLink,
                SortOrder = m.SortOrder,
                CreatedDate = m.CreatedDate,
                ModifiedDate = m.ModifiedDate,
                CreatedBy = m.CreatedBy,
                ModifiedBy = m.ModifiedBy,
                Module = m.Module,
                Icon = m.Icon,
                AssignedRoleIds = m.RoleMenus.Select(rm => rm.RoleId).ToList(),
                AssignedRoles = m.RoleMenus.Select(rm => new RoleDto
                {
                    RoleId = rm.Role.RoleId,
                    RoleTitle = rm.Role.RoleTitle,
                    RoleIsActive = rm.Role.RoleIsActive
                }).ToList()
            })
            .FirstOrDefault();

        return Task.FromResult(menu);
    }

    public Task<MenuDto> CreateMenuAsync(MenuDto menuDto)
    {
        var menu = new Menu
        {
            MemuTitle = menuDto.MemuTitle,
            MenuLink = menuDto.MenuLink,
            SortOrder = menuDto.SortOrder,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = menuDto.CreatedBy,
            ModifiedBy = menuDto.ModifiedBy,
            Module = menuDto.Module,
            Icon = menuDto.Icon
        };

        context.Menus.Add(menu);
        context.SaveChanges();

        // Assign roles to menu
        if (menuDto.AssignedRoleIds.Any())
        {
            var roleMenus = menuDto.AssignedRoleIds.Select(roleId => new RoleMenu
            {
                RoleId = roleId,
                MenuId = menu.MenuId,
                CreatedDate = DateTime.Now,
                CreatedBy = menuDto.CreatedBy
            }).ToList();

            context.RoleMenus.AddRange(roleMenus);
            context.SaveChanges();
        }

        menuDto.MenuId = menu.MenuId;
        return Task.FromResult(menuDto);
    }

    public Task<bool> UpdateMenuAsync(MenuDto menuDto)
    {
        var menu = context.Menus.Find(menuDto.MenuId);
        if (menu == null)
        {
            return Task.FromResult(false);
        }

        menu.MemuTitle = menuDto.MemuTitle;
        menu.MenuLink = menuDto.MenuLink;
        menu.SortOrder = menuDto.SortOrder;
        menu.ModifiedDate = DateTime.Now;
        menu.ModifiedBy = menuDto.ModifiedBy;
        menu.Module = menuDto.Module;
        menu.Icon = menuDto.Icon;

        // Update menu roles
        var existingRoleMenus = context.RoleMenus.Where(rm => rm.MenuId == menuDto.MenuId).ToList();
        context.RoleMenus.RemoveRange(existingRoleMenus);

        if (menuDto.AssignedRoleIds.Any())
        {
            var newRoleMenus = menuDto.AssignedRoleIds.Select(roleId => new RoleMenu
            {
                RoleId = roleId,
                MenuId = menuDto.MenuId,
                CreatedDate = DateTime.Now,
                CreatedBy = menuDto.ModifiedBy
            }).ToList();

            context.RoleMenus.AddRange(newRoleMenus);
        }

        context.SaveChanges();
        return Task.FromResult(true);
    }

    public Task<bool> DeleteMenuAsync(int id)
    {
        var menu = context.Menus.Find(id);
        if (menu == null)
        {
            return Task.FromResult(false);
        }

        // Remove role menus first
        var roleMenus = context.RoleMenus.Where(rm => rm.MenuId == id).ToList();
        context.RoleMenus.RemoveRange(roleMenus);

        context.Menus.Remove(menu);
        context.SaveChanges();
        return Task.FromResult(true);
    }

    public Task<List<MenuDto>> GetMenusByModuleAsync(string? module)
    {
        var query = context.Menus.AsQueryable();
        
        if (string.IsNullOrEmpty(module))
        {
            query = query.Where(m => string.IsNullOrEmpty(m.Module));
        }
        else
        {
            query = query.Where(m => m.Module == module);
        }

        var menus = query
            .Select(m => new MenuDto
            {
                MenuId = m.MenuId,
                MemuTitle = m.MemuTitle,
                MenuLink = m.MenuLink,
                SortOrder = m.SortOrder,
                Module = m.Module,
                Icon = m.Icon
            })
            .OrderBy(m => m.SortOrder)
            .ThenBy(m => m.MemuTitle)
            .ToList();

        return Task.FromResult(menus);
    }

    public Task<List<string>> GetModulesAsync()
    {
        var modules = context.Menus
            .Where(m => !string.IsNullOrEmpty(m.Module))
            .Select(m => m.Module!)
            .Distinct()
            .OrderBy(m => m)
            .ToList();

        return Task.FromResult(modules);
    }
}
