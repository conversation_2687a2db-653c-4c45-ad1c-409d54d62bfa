﻿@page "/"
@using Microsoft.FluentUI.AspNetCore.Components.Icons
<PageTitle>GEL Programs Inventory</PageTitle>

<div class="dashboard-container">
    <div class="header-section">
        <h1 class="dashboard-title">
            <FluentIcon Icon="Icons.Regular.Size24.Home" />
            GEL Programs Inventory
        </h1>
        <p class="dashboard-subtitle">Welcome to the GEL Programs Inventory Management System</p>
        <p class="dashboard-description">
            Use the navigation menu to access Programs, Syndications, Clients, and Production Houses.
        </p>
    </div>

    <div class="welcome-section">
        <div class="welcome-content">
            <h2>Quick Start</h2>
            <ul>
                <li>Navigate to <strong>Programs</strong> to manage your television program inventory</li>
                <li>Use <strong>Syndications</strong> to track program distribution and licensing</li>
                <li>Manage <strong>Clients</strong> and <strong>Production Houses</strong> for comprehensive data management</li>
            </ul>
        </div>
    </div>


</div>

<style>
    .dashboard-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header-section {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .dashboard-title {
        font-size: 2.5rem;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
    }

    .dashboard-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0 0 15px 0;
    }

    .dashboard-description {
        font-size: 1rem;
        opacity: 0.8;
        margin: 0;
    }

    .welcome-section {
        background: #f8f9fa;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }

    .welcome-content h2 {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.8rem;
    }

    .welcome-content ul {
        text-align: left;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.8;
    }

    .welcome-content li {
        margin-bottom: 10px;
        color: #555;
    }

    @@media (max-width: 768px) {
        .dashboard-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 10px;
        }

        .welcome-content ul {
            text-align: left;
        }
    }
</style>