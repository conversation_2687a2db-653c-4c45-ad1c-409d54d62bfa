﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace GELProgramsInventory.Models;

public partial class Program
{
    public int ProgramId { get; set; }

    public string ProgramName { get; set; }

    public int? GenreId { get; set; }

    public int? OnAirFormatId { get; set; }

    public string YearOfLaunch { get; set; }

    public string DriveSerialNumber { get; set; }

    public bool? IsSyndication { get; set; }

    public int? SyndicationFormatId { get; set; }

    public int? ClientId { get; set; }

    public int? ProductionHouseId { get; set; }

    public string Remark { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool? IsActive { get; set; }

    public string SecondaryName1 { get; set; }

    public string SecondaryName2 { get; set; }

    public string SecondaryName3 { get; set; }

    public string SecondaryName4 { get; set; }

    public int TotalEpisodes { get; set; }

    public string SyndicationDeliveryRemarks { get; set; }

    public int? Duration { get; set; }

    public virtual Client Client { get; set; }

    public virtual Genre Genre { get; set; }

    public virtual OnAirFormat OnAirFormat { get; set; }

    public virtual ProductionHouse ProductionHouse { get; set; }

    public virtual SyndicationFormat SyndicationFormat { get; set; }

    public virtual ICollection<Syndication> Syndications { get; set; } = new List<Syndication>();
}