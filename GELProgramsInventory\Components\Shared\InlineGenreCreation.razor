@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@inject GenreService GenreService

<SfDialog @ref="GenreDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Add New Genre</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="NewGenre" OnValidSubmit="SaveGenre" FormName="InlineGenreForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary />
                    
                    <div class="form-group mb-3">
                        <label>Genre Name</label>
                        <SfTextBox @bind-value="NewGenre.GenreName" Placeholder="Enter genre name" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewGenre.GenreName)" />
                    </div>
                    
                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card-body {
        padding: 1.5rem;
    }
    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>

@code {
    private SfDialog? GenreDialog;
    private GenreDto NewGenre = new();

    [Parameter] public EventCallback<GenreDto> OnGenreCreated { get; set; }

    public async Task ShowDialog()
    {
        NewGenre = new GenreDto { IsActive = true };
        if (GenreDialog != null)
        {
            await GenreDialog.ShowAsync();
        }
    }

    private async Task SaveGenre()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(NewGenre.GenreName))
            {
                return;
            }

            var createdGenre = await GenreService.CreateGenreAsync(NewGenre);
            
            NewGenre.GenreId = createdGenre.GenreId;
            
            await OnGenreCreated.InvokeAsync(NewGenre);
            await CloseDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating genre: {ex.Message}");
        }
    }

    private async Task CloseDialog()
    {
        if (GenreDialog != null)
        {
            await GenreDialog.HideAsync();
        }
        NewGenre = new();
    }
}