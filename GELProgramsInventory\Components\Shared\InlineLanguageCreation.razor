@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@inject LanguageService LanguageService

<SfDialog @ref="LanguageDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Add New Language</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="NewLanguage" OnValidSubmit="SaveLanguage" FormName="InlineLanguageForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary />
                    
                    <div class="form-group mb-3">
                        <label>Language Name</label>
                        <SfTextBox @bind-value="NewLanguage.Title" Placeholder="Enter language name" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewLanguage.Title)" />
                    </div>
                    
                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card-body {
        padding: 1.5rem;
    }
    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>

@code {
    private SfDialog? LanguageDialog;
    private LanguageDto NewLanguage = new();

    [Parameter] public EventCallback<LanguageDto> OnLanguageCreated { get; set; }

    public async Task ShowDialog()
    {
        NewLanguage = new LanguageDto();
        if (LanguageDialog != null)
        {
            await LanguageDialog.ShowAsync();
        }
    }

    private async Task SaveLanguage()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(NewLanguage.Title))
            {
                return;
            }

            var createdLanguage = await LanguageService.CreateLanguageAsync(NewLanguage);
            
            NewLanguage.LanguageId = createdLanguage.LanguageId;
            NewLanguage.Name = createdLanguage.Title;
            
            await OnLanguageCreated.InvokeAsync(NewLanguage);
            await CloseDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating language: {ex.Message}");
        }
    }

    private async Task CloseDialog()
    {
        if (LanguageDialog != null)
        {
            await LanguageDialog.HideAsync();
        }
        NewLanguage = new();
    }
}
