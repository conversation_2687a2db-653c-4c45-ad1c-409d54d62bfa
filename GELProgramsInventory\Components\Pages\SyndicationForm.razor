@page "/syndications/add"
@page "/syndications/edit/{SyndicationId:int}"
@using GELProgramsInventory.Components.Shared
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@inject SyndicationService SyndicationService
@inject ProgramService ProgramService
@inject ClientService ClientService
@inject SyndicationFormatService SyndicationFormatService
@inject LanguageService LanguageService
@inject OnAirFormatService OnAirFormatService
@inject CurrencyService CurrencyService
@inject TerritoryService TerritoryService
@inject ContractDocumentService ContractDocumentService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Syndications" SubPage="@(IsEditMode ? "Edit Syndication" : "Add Syndication")"/>

<div class="">
    <div class="card">
        <div class="card-header">
            @(IsEditMode ? "Edit Syndication" : "Add New Syndication")
        </div>
        <div class="card-body">
            <EditForm Model="CurrentSyndication" OnValidSubmit="SaveSyndication" FormName="SyndicationForm">
                <DataAnnotationsValidator/>
                <ValidationSummary class="validation-summary"/>

                <div class="form-section">
                    <h5 class="section-title">Program Selection</h5>
                    <div class="form-grid">
                        <div class="form-group full-width">
                            <label class="required">Program</label>
                            <SfDropDownList TValue="int?" TItem="ProgramDto" DataSource="Programs" @bind-value="CurrentSyndication.ProgramId" Placeholder="— Select Program —" FloatLabelType="FloatLabelType.Never" Enabled="@(!IsEditMode)">
                                <DropDownListFieldSettings Text="PrimaryName" Value="ProgramId"></DropDownListFieldSettings>
                            </SfDropDownList>
                            <ValidationMessage For="@(() => CurrentSyndication.ProgramId)"/>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="section-title">Syndication Details</h5>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="required">Client</label>
                            <div class="dropdown-with-add">
                                <SfDropDownList TValue="int?" TItem="ClientDto" DataSource="Clients" @bind-value="CurrentSyndication.ClientId" Placeholder="— Select Client —" FloatLabelType="FloatLabelType.Never">
                                    <DropDownListFieldSettings Text="ClientName" Value="ClientId"></DropDownListFieldSettings>
                                </SfDropDownList>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddClientDialog">Add New</SfButton>
                            </div>
                            <ValidationMessage For="@(() => CurrentSyndication.ClientId)"/>
                        </div>

                        <div class="form-group">
                            <label class="required">Syndication Format</label>
                            <div class="dropdown-with-add">
                                <SfDropDownList TValue="int?" TItem="SyndicationFormatDto" DataSource="SyndicationFormats" @bind-value="CurrentSyndication.SyndicationFormatId" Placeholder="— Select Format —" FloatLabelType="FloatLabelType.Never">
                                    <DropDownListFieldSettings Text="FormatName" Value="SyndicationFormatId"></DropDownListFieldSettings>
                                </SfDropDownList>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddFormatDialog">Add New</SfButton>
                            </div>
                            <ValidationMessage For="@(() => CurrentSyndication.SyndicationFormatId)"/>
                        </div>

                        <div class="form-group">
                            <label class="required">Language</label>
                            <div class="dropdown-with-add">
                                <SfDropDownList TValue="int?" TItem="LanguageDto" DataSource="Languages" @bind-value="CurrentSyndication.LanguageId" Placeholder="— Select Language —" FloatLabelType="FloatLabelType.Never">
                                    <DropDownListFieldSettings Text="Title" Value="LanguageId"></DropDownListFieldSettings>
                                </SfDropDownList>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddLanguageDialog">Add New</SfButton>
                            </div>
                            <ValidationMessage For="@(() => CurrentSyndication.LanguageId)"/>
                        </div>

                        <div class="form-group">
                            <label>On Air Format</label>
                            <div class="dropdown-with-add">
                                <SfDropDownList TValue="int?" TItem="OnAirFormatDto" DataSource="OnAirFormats" @bind-value="CurrentSyndication.OnAirFormatId" Placeholder="— Select On Air Format —" FloatLabelType="FloatLabelType.Never">
                                    <DropDownListFieldSettings Text="@nameof(OnAirFormatDto.OnAirFormatName)" Value="@nameof(OnAirFormatDto.OnAirFormatId)"></DropDownListFieldSettings>
                                </SfDropDownList>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddOnAirFormatDialog">Add New</SfButton>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="section-title">Contract Information</h5>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Contract Start Date</label>
                            <SfDatePicker TValue="DateTime?" @bind-Value="CurrentSyndication.ContractStartDate" Placeholder="Select start date" FloatLabelType="FloatLabelType.Never"></SfDatePicker>
                        </div>

                        <div class="form-group">
                            <label>Contract End Date</label>
                            <SfDatePicker TValue="DateTime?" @bind-Value="CurrentSyndication.ContractEndDate" Placeholder="Select end date" FloatLabelType="FloatLabelType.Never"></SfDatePicker>
                        </div>

                        <div class="form-group">
                            <label>Territory</label>
                            <div class="dropdown-with-add">
                                <SfDropDownList TValue="int?" TItem="TerritoryDto" DataSource="Territories" @bind-value="CurrentSyndication.TerritoryId" Placeholder="— Select Territory —" FloatLabelType="FloatLabelType.Never">
                                    <DropDownListFieldSettings Text="Name" Value="TerritoryId"></DropDownListFieldSettings>
                                </SfDropDownList>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddTerritoryDialog">Add New</SfButton>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="section-title">Episode and Pricing Details</h5>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Episode From</label>
                            <SfNumericTextBox @bind-Value="CurrentSyndication.EpisodeFrom" Format="n0" Placeholder="Enter starting episode" FloatLabelType="FloatLabelType.Never" Min="0"></SfNumericTextBox>
                        </div>

                        <div class="form-group">
                            <label>Episode To</label>
                            <SfNumericTextBox @bind-Value="CurrentSyndication.EpisodeTo" Format="n0" Placeholder="Enter ending episode" FloatLabelType="FloatLabelType.Never" Min="0"></SfNumericTextBox>
                        </div>

                        <div class="form-group">
                            <label>Price Per Episode</label>
                            <SfNumericTextBox @bind-Value="CurrentSyndication.PricePerEpisode" Format="n0" Placeholder="Enter price per episode" FloatLabelType="FloatLabelType.Never" Min="0"></SfNumericTextBox>
                        </div>

                        <div class="form-group">
                            <label>Currency</label>
                            <div class="dropdown-with-add">
                                <SfDropDownList TValue="int?" TItem="CurrencyDto" DataSource="Currencies" @bind-value="CurrentSyndication.CurrencyId" Placeholder="— Select Currency —" FloatLabelType="FloatLabelType.Never">
                                    <DropDownListFieldSettings Text="Code" Value="CurrencyId"></DropDownListFieldSettings>
                                </SfDropDownList>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddCurrencyDialog">Add New</SfButton>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="section-title">Additional Information</h5>
                    <div class="form-grid">
                        <div class="form-group full-width">
                            <label>Remarks</label>
                            <SfTextBox @bind-value="CurrentSyndication.Remarks" Placeholder="Enter any additional remarks or notes" FloatLabelType="FloatLabelType.Never" Multiline="true" Rows="4"></SfTextBox>
                        </div>
                    </div>
                </div>

                @if (IsEditMode && CurrentSyndication.Id > 0)
                {
                    <div class="form-section">
                        <ContractDocumentUpload SyndicationId="CurrentSyndication.Id"
                                              OnDocumentUploaded="OnDocumentUploaded"
                                              OnDocumentDeleted="OnDocumentDeleted"
                                              @ref="ContractDocumentUploadComponent" />
                    </div>
                }
                else if (!IsEditMode)
                {
                    <div class="form-section">
                        <div class="info-message">
                            <i class="e-icons e-info"></i>
                            <span>Contract documents can be uploaded after saving the syndication.</span>
                        </div>
                    </div>
                }

                <div class="form-actions">
                    <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">@(IsEditMode ? "Update Syndication" : "Save Syndication")</SfButton>
                    <SfButton OnClick="CancelForm" CssClass="e-secondary">Cancel</SfButton>
                </div>
            </EditForm>
        </div>
    </div>
</div>

<!-- Inline Creation Components -->
<InlineClientCreation @ref="ClientCreationComponent" OnClientCreated="OnClientCreated"/>
<InlineSyndicationFormatCreation @ref="FormatCreationComponent" OnFormatCreated="OnFormatCreated"/>
<InlineLanguageCreation @ref="LanguageCreationComponent" OnLanguageCreated="OnLanguageCreated"/>
<InlineOnAirFormatCreation @ref="OnAirFormatCreationComponent" OnOnAirFormatCreated="OnOnAirFormatCreated"/>
<InlineCurrencyCreation @ref="CurrencyCreationComponent" OnCurrencyCreated="OnCurrencyCreated"/>
<InlineTerritoryCreation @ref="TerritoryCreationComponent" OnTerritoryCreated="OnTerritoryCreated"/>

<style>
    .syndication-form-container {
        max-width: 900px;
        margin: 2rem auto;
        padding: 1rem;
    }

    .card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    }

    .card-header {
        background-color: #f5f5f5;
        padding: 1rem 1.5rem;
        font-size: 1.25rem;
        font-weight: 500;
        border-bottom: 1px solid #e0e0e0;
    }

    .card-body {
        padding: 1.5rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    @@media (min-width: 768px) {
        .form-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .form-group label.required::after {
        content: " *";
        color: #dc3545;
    }

    .dropdown-with-add {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .dropdown-with-add .e-dropdownlist {
        flex-grow: 1;
    }

    .add-button {
        flex-shrink: 0;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e0e0e0;
    }

    .validation-summary {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 4px;
        margin-bottom: 1.5rem;
    }

    .info-message {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        padding: 1rem;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-message .e-icons {
        font-size: 1.2rem;
    }
</style>

@code {
    [Parameter] public int? SyndicationId { get; set; }
    [Parameter] [SupplyParameterFromQuery] public int? ProgramId { get; set; }

    private SyndicationDto CurrentSyndication = new();
    private bool IsEditMode => SyndicationId.HasValue && SyndicationId.Value > 0;

    private List<ProgramDto> Programs = new();
    private List<ClientDto> Clients = new();
    private List<SyndicationFormatDto> SyndicationFormats = new();
    private List<LanguageDto> Languages = new();
    private List<OnAirFormatDto> OnAirFormats = new();
    private List<TerritoryDto> Territories = new();
    private List<CurrencyDto> Currencies = new();

    private InlineClientCreation? ClientCreationComponent;
    private InlineSyndicationFormatCreation? FormatCreationComponent;
    private InlineLanguageCreation? LanguageCreationComponent;
    private InlineOnAirFormatCreation? OnAirFormatCreationComponent;
    private InlineCurrencyCreation? CurrencyCreationComponent;
    private InlineTerritoryCreation? TerritoryCreationComponent;
    private ContractDocumentUpload? ContractDocumentUploadComponent;

    protected override async Task OnInitializedAsync()
    {
        await LoadDropdownData();

        if (IsEditMode)
        {
            var syndication = await SyndicationService.GetSyndicationByIdAsync(SyndicationId!.Value);
            if (syndication != null)
            {
                CurrentSyndication = syndication;
            }
            else
            {
                Navigation.NavigateTo("/syndications");
            }
        }
        else
        {
            CurrentSyndication = new SyndicationDto();
            if (ProgramId.HasValue)
            {
                CurrentSyndication.ProgramId = ProgramId.Value;
            }
        }
    }

    private async Task LoadDropdownData()
    {
        var programsTask = ProgramService.GetProgramsAsync();
        var clientsTask = ClientService.GetClientsAsync();
        var formatsTask = SyndicationFormatService.GetSyndicationFormatDtosAsync();
        var languagesTask = LanguageService.GetLanguagesAsync();
        var onAirFormatsTask = OnAirFormatService.GetOnAirFormatDtosAsync();

        // Wait for all tasks to complete
        await Task.WhenAll(programsTask, clientsTask, formatsTask, languagesTask, onAirFormatsTask);

        // Assign results
        Programs = await programsTask;
        Clients = await clientsTask;
        SyndicationFormats = await formatsTask;
        Languages = await languagesTask;
        OnAirFormats = await onAirFormatsTask;

        // Load territories and currencies
        try
        {
            // Load territories from the database
            var territoriesTask = TerritoryService.GetTerritoriesAsync();
            
            // Load currencies from the database
            var currenciesTask = CurrencyService.GetCurrencyDtosAsync();
            
            // Wait for both tasks to complete
            await Task.WhenAll(territoriesTask, currenciesTask);
            
            // Assign results
            Territories = await territoriesTask;
            Currencies = await currenciesTask;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading territory or currency data: {ex.Message}");
        }
    }

    private async Task SaveSyndication()
    {
        try
        {
            if (!CurrentSyndication.ProgramId.HasValue ||
                !CurrentSyndication.ClientId.HasValue ||
                !CurrentSyndication.SyndicationFormatId.HasValue ||
                !CurrentSyndication.LanguageId.HasValue)
            {
                return;
            }

            if (IsEditMode)
            {
                await SyndicationService.UpdateSyndicationAsync(CurrentSyndication);
                Navigation.NavigateTo("/syndications");
            }
            else
            {
                var createdSyndication = await SyndicationService.CreateSyndicationAsync(CurrentSyndication);
                // Navigate to edit mode to allow document upload
                Navigation.NavigateTo($"/syndications/edit/{createdSyndication.Id}", true);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving syndication: {ex.Message}");
        }
    }

    private void CancelForm()
    {
        Navigation.NavigateTo("/syndications");
    }

    private async Task ShowAddClientDialog()
    {
        if (ClientCreationComponent != null)
        {
            await ClientCreationComponent.ShowDialog();
        }
    }

    private async Task ShowAddFormatDialog()
    {
        if (FormatCreationComponent != null)
        {
            await FormatCreationComponent.ShowDialog();
        }
    }

    private async Task ShowAddLanguageDialog()
    {
        if (LanguageCreationComponent != null)
        {
            await LanguageCreationComponent.ShowDialog();
        }
    }

    private void OnClientCreated(ClientDto newClient)
    {
        Clients.Add(newClient);
        CurrentSyndication.ClientId = newClient.ClientId;
        StateHasChanged();
    }

    private void OnFormatCreated(SyndicationFormatDto newFormat)
    {
        SyndicationFormats.Add(newFormat);
        CurrentSyndication.SyndicationFormatId = newFormat.SyndicationFormatId;
        StateHasChanged();
    }

    private void OnLanguageCreated(LanguageDto newLanguage)
    {
        Languages.Add(newLanguage);
        CurrentSyndication.LanguageId = newLanguage.LanguageId;
        StateHasChanged();
    }

    private async Task ShowAddOnAirFormatDialog()
    {
        if (OnAirFormatCreationComponent != null)
        {
            await OnAirFormatCreationComponent.ShowDialog();
        }
    }

    private void OnOnAirFormatCreated(OnAirFormatDto newOnAirFormat)
    {
        OnAirFormats.Add(newOnAirFormat);
        CurrentSyndication.OnAirFormatId = newOnAirFormat.OnAirFormatId;
        StateHasChanged();
    }

    private async Task ShowAddCurrencyDialog()
    {
        if (CurrencyCreationComponent != null)
        {
            await CurrencyCreationComponent.ShowDialog();
        }
    }

    private void OnCurrencyCreated(CurrencyDto newCurrency)
    {
        Currencies.Add(newCurrency);
        CurrentSyndication.CurrencyId = newCurrency.CurrencyId;
        StateHasChanged();
    }

    private async Task ShowAddTerritoryDialog()
    {
        if (TerritoryCreationComponent != null)
        {
            await TerritoryCreationComponent.ShowDialog();
        }
    }

    private void OnTerritoryCreated(TerritoryDto newTerritory)
    {
        Territories.Add(newTerritory);
        CurrentSyndication.TerritoryId = newTerritory.TerritoryId;
        StateHasChanged();
    }

    private void OnDocumentUploaded(ContractDocumentDto document)
    {
        // Document uploaded successfully
        StateHasChanged();
    }

    private void OnDocumentDeleted(Guid documentId)
    {
        // Document deleted successfully
        StateHasChanged();
    }
}