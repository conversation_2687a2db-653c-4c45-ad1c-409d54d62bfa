using GELProgramsInventory.Dto;
using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class ProgramService(ApplicationDbContext context)
{
    public Task<List<ProgramDto>> GetProgramsAsync()
    {
        var res = context.Programs
            .Include(p => p.Genre)
            .Include(p => p.ProductionHouse)
            .Include(p => p.OnAirFormat)
            .Where(p => p.IsActive == true)
            .Select(p => new ProgramDto
            {
                ProgramId = p.ProgramId,
                // Map to new naming structure - for now use ProgramName as PrimaryName
                PrimaryName = p.ProgramName,
                ProgramName = p.ProgramName, // Keep for backward compatibility
                GenreId = p.GenreId,
                GenreName = p.Genre.GenreName,
                ProductionHouseId = p.ProductionHouseId,
                ProductionHouseName = p.ProductionHouse.ProductionHouseName,
                OnAirFormatId = p.OnAirFormatId,
                OnAirFormatName = p.OnAirFormat.FormatName,
                Remarks = p.Remark,
                DriveSerialNumber = p.DriveSerialNumber,
                YearOfLaunch = p.YearOfLaunch,
                IsActive = p.IsActive,
                // New fields
                SyndicationDeliveryRemarks = p.SyndicationDeliveryRemarks,
                TotalEpisodes = p.TotalEpisodes,
                SecondaryName1 = p.SecondaryName1,
                SecondaryName2 = p.SecondaryName2,
                SecondaryName3 = p.SecondaryName3,
                SecondaryName4 = p.SecondaryName4,
                Duration = p.Duration
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<List<TreeGridItemDto>> GetProgramsForTreeGridAsync()
    {
        var q = (from a in context.Programs
            select new TreeGridItemDto
            {
                IsActive = a.IsActive == true, Details = a.Remark, Id = a.ProgramId,
                Name = $"{a.Genre.GenreName}: {a.ProgramName}",
                ParentId = null, Type = "Program"
            }).ToList();
        var s = (from a in context.Syndications
                orderby a.Client.ClientName
                select new TreeGridItemDto
                {
                    IsActive = true, Name = $"Syndication: {a.Client.ClientName}",
                    Details = $"Format: {a.SyndicationFormat.FormatName}, Language: {a.Language.Title}",
                    ParentId = a.ProgramId, Type = "Syndication", Id = a.Id
                }
            ).ToList();
        return Task.FromResult(q.Union(s).ToList());
    }

    public Task<List<ProgramDto>> GetProgramsForTreeGridAsync2()
    {
        var res = context.Programs
            .Include(p => p.Genre)
            .Include(p => p.ProductionHouse)
            .Include(p => p.OnAirFormat)
            .Include(p => p.Syndications)
            .Where(p => p.IsActive == true)
            .Select(p => new ProgramDto
            {
                ProgramId = p.ProgramId,
                PrimaryName = p.ProgramName,
                ProgramName = p.ProgramName,
                GenreId = p.GenreId,
                GenreName = p.Genre.GenreName,
                ProductionHouseId = p.ProductionHouseId,
                ProductionHouseName = p.ProductionHouse.ProductionHouseName,
                OnAirFormatId = p.OnAirFormatId,
                OnAirFormatName = p.OnAirFormat.FormatName,
                Remarks = p.Remark,
                DriveSerialNumber = p.DriveSerialNumber,
                YearOfLaunch = p.YearOfLaunch,
                IsActive = p.IsActive,
                // New fields
                SyndicationDeliveryRemarks = p.SyndicationDeliveryRemarks,
                TotalEpisodes = p.TotalEpisodes,
                SecondaryName1 = p.SecondaryName1,
                SecondaryName2 = p.SecondaryName2,
                SecondaryName3 = p.SecondaryName3,
                SecondaryName4 = p.SecondaryName4
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<ProgramDto?> GetProgramByIdAsync(int id)
    {
        var program = context.Programs
            .Include(p => p.Genre)
            .Include(p => p.ProductionHouse)
            .Include(p => p.OnAirFormat)
            .FirstOrDefault(p => p.ProgramId == id);

        if (program == null) return Task.FromResult<ProgramDto?>(null);

        var res = new ProgramDto
        {
            ProgramId = program.ProgramId,
            PrimaryName = program.ProgramName,
            ProgramName = program.ProgramName, // Keep for backward compatibility
            GenreId = program.GenreId,
            GenreName = program.Genre?.GenreName,
            ProductionHouseId = program.ProductionHouseId,
            ProductionHouseName = program.ProductionHouse?.ProductionHouseName,
            OnAirFormatId = program.OnAirFormatId,
            OnAirFormatName = program.OnAirFormat?.FormatName,
            Remarks = program.Remark,
            DriveSerialNumber = program.DriveSerialNumber,
            YearOfLaunch = program.YearOfLaunch,
            IsActive = program.IsActive,
            SyndicationDeliveryRemarks = program.SyndicationDeliveryRemarks,
            TotalEpisodes = program.TotalEpisodes,
            SecondaryName1 = program.SecondaryName1,
            SecondaryName2 = program.SecondaryName2,
            SecondaryName3 = program.SecondaryName3,
            SecondaryName4 = program.SecondaryName4,
            Duration = program.Duration
        };
        return Task.FromResult<ProgramDto?>(res);
    }

    public Task<Models.Program> CreateProgramAsync(ProgramDto programDto)
    {
        var program = new Models.Program
        {
            // Use PrimaryName if available, otherwise fall back to ProgramName
            ProgramName = !string.IsNullOrEmpty(programDto.PrimaryName)
                ? programDto.PrimaryName
                : programDto.ProgramName,
            GenreId = programDto.GenreId,
            ProductionHouseId = programDto.ProductionHouseId,
            OnAirFormatId = programDto.OnAirFormatId,
            Remark = programDto.Remarks,
            DriveSerialNumber = programDto.DriveSerialNumber,
            YearOfLaunch = programDto.YearOfLaunch,
            IsActive = programDto.IsActive ?? true,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            // New fields
            SyndicationDeliveryRemarks = programDto.SyndicationDeliveryRemarks,
            TotalEpisodes = programDto.TotalEpisodes ?? 1,
            SecondaryName1 = programDto.SecondaryName1,
            SecondaryName2 = programDto.SecondaryName2,
            SecondaryName3 = programDto.SecondaryName3,
            SecondaryName4 = programDto.SecondaryName4,
            Duration = programDto.Duration
        };

        context.Programs.Add(program);
        context.SaveChanges();
        return Task.FromResult(program);
    }

    public Task UpdateProgramAsync(ProgramDto programDto)
    {
        var program = context.Programs.Find(programDto.ProgramId);
        if (program != null)
        {
            program.ProgramName = !string.IsNullOrEmpty(programDto.PrimaryName)
                ? programDto.PrimaryName
                : programDto.ProgramName;
            program.GenreId = programDto.GenreId;
            program.ProductionHouseId = programDto.ProductionHouseId;
            program.OnAirFormatId = programDto.OnAirFormatId;
            program.Remark = programDto.Remarks;
            program.DriveSerialNumber = programDto.DriveSerialNumber;
            program.YearOfLaunch = programDto.YearOfLaunch;
            program.IsActive = programDto.IsActive;
            program.ModifiedDate = DateTime.Now;
            // New fields
            program.SyndicationDeliveryRemarks = programDto.SyndicationDeliveryRemarks;
            program.TotalEpisodes = programDto.TotalEpisodes ?? 1;
            program.SecondaryName1 = programDto.SecondaryName1;
            program.SecondaryName2 = programDto.SecondaryName2;
            program.SecondaryName3 = programDto.SecondaryName3;
            program.SecondaryName4 = programDto.SecondaryName4;
            program.Duration = programDto.Duration;
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    public Task DeleteProgramAsync(int id)
    {
        var program = context.Programs.Find(id);
        if (program != null)
        {
            context.Programs.Remove(program);
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    // Soft delete method
    public Task SoftDeleteProgramAsync(int id)
    {
        var program = context.Programs.Find(id);
        if (program != null)
        {
            program.IsActive = false;
            program.ModifiedDate = DateTime.Now;
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    public Task<bool> IsProgramNameUniqueAsync(string programName, int? excludeProgramId = null)
    {
        var query = context.Programs.Where(p => p.ProgramName == programName && p.IsActive == true);

        if (excludeProgramId.HasValue) query = query.Where(p => p.ProgramId != excludeProgramId.Value);

        return Task.FromResult(!query.Any());
    }
}