using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;

namespace GELProgramsInventory.Services;

public class CurrencyService(ApplicationDbContext context)
{
    public Task<List<Currency>> GetCurrenciesAsync()
    {
        var res = context.Currencies.ToList();
        return Task.FromResult(res);
    }

    public Task<List<CurrencyDto>> GetCurrencyDtosAsync()
    {
        var res = context.Currencies
            .Select(c => new CurrencyDto
            {
                CurrencyId = c.CurrencyId,
                Code = c.Code,
                Name = c.Title
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<CurrencyDto?> GetCurrencyByIdAsync(int id)
    {
        var currency = context.Currencies.Find(id);
        if (currency == null) return Task.FromResult<CurrencyDto?>(null);

        var res = new CurrencyDto
        {
            CurrencyId = currency.CurrencyId,
            Code = currency.Code,
            Name = currency.Title
        };
        return Task.FromResult<CurrencyDto?>(res);
    }

    public Task<Currency> CreateCurrencyAsync(CurrencyDto currencyDto)
    {
        var currency = new Currency
        {
            Title = currencyDto.Name,
            Code = currencyDto.Code,
            CreateDate = DateTime.Now,
            ModifiedDate = DateTime.Now
        };

        context.Currencies.Add(currency);
        context.SaveChanges();
        return Task.FromResult(currency);
    }

    public Task UpdateCurrencyAsync(CurrencyDto currencyDto)
    {
        var currency = context.Currencies.Find(currencyDto.CurrencyId);
        if (currency != null)
        {
            currency.Title = currencyDto.Name;
            currency.Code = currencyDto.Code;
            currency.ModifiedDate = DateTime.Now;
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    public Task DeleteCurrencyAsync(int id)
    {
        var currency = context.Currencies.Find(id);
        if (currency != null)
        {
            context.Currencies.Remove(currency);
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }
}