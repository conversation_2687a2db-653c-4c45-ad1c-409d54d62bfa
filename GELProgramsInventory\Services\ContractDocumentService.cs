using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class ContractDocumentService(ApplicationDbContext context)
{
    public Task<List<ContractDocumentDto>> GetContractDocumentsBySyndicationIdAsync(int syndicationId)
    {
        var documents = context.ContractDocuments
            .Where(cd => cd.SyndicationId == syndicationId && cd.IsActive)
            .Include(cd => cd.Syndication)
            .ThenInclude(s => s.Program)
            .OrderByDescending(cd => cd.UploadedDate)
            .Select(cd => new ContractDocumentDto
            {
                Id = cd.Id,
                FileName = cd.FileName,
                SyndicationId = cd.SyndicationId,
                UploadedDate = cd.UploadedDate,
                UploadedBy = cd.UploadedBy,
                IsActive = cd.IsActive,
                SyndicationName = cd.Syndication.Program.ProgramName,
                FileSizeBytes = cd.FileContent != null ? cd.FileContent.Length : 0
            })
            .ToList();

        return Task.FromResult(documents);
    }

    public Task<ContractDocumentDto?> GetContractDocumentByIdAsync(Guid id)
    {
        var document = context.ContractDocuments
            .Where(cd => cd.Id == id && cd.IsActive)
            .Include(cd => cd.Syndication)
            .ThenInclude(s => s.Program)
            .FirstOrDefault();

        if (document == null) return null;

        var res = new ContractDocumentDto
        {
            Id = document.Id,
            FileName = document.FileName,
            FileContent = document.FileContent,
            SyndicationId = document.SyndicationId,
            UploadedDate = document.UploadedDate,
            UploadedBy = document.UploadedBy,
            IsActive = document.IsActive,
            SyndicationName = document.Syndication.Program.ProgramName,
            FileSizeBytes = document.FileContent?.Length ?? 0
        };
        return Task.FromResult(res)!;
    }

    public Task<ContractDocumentDto> CreateContractDocumentAsync(ContractDocumentDto documentDto, string uploadedBy)
    {
        var document = new ContractDocument
        {
            Id = Guid.NewGuid(),
            FileName = documentDto.FileName,
            FileContent = documentDto.FileContent,
            SyndicationId = documentDto.SyndicationId,
            UploadedDate = DateTime.Now,
            UploadedBy = uploadedBy,
            IsActive = true
        };

        context.ContractDocuments.Add(document);
        context.SaveChanges();

        // Return the created document with additional info
        // return GetContractDocumentByIdAsync(document.Id) ?? documentDto;
        return Task.FromResult(new ContractDocumentDto
        {
            Id = document.Id,
            FileName = document.FileName,
            SyndicationId = document.SyndicationId,
            UploadedDate = document.UploadedDate,
            UploadedBy = document.UploadedBy,
            IsActive = document.IsActive,
            SyndicationName = context.Syndications
                .Where(s => s.Id == document.SyndicationId)
                .Select(s => s.Program.ProgramName)
                .FirstOrDefault(),
            FileSizeBytes = document.FileContent?.Length ?? 0
        });
    }

    public async Task<bool> DeleteContractDocumentAsync(Guid id)
    {
        var document = await context.ContractDocuments.FindAsync(id);
        if (document == null) return false;

        // Soft delete
        document.IsActive = false;
        await context.SaveChangesAsync();
        return true;
    }

    public async Task<byte[]?> GetFileContentAsync(Guid id)
    {
        var document = await context.ContractDocuments
            .Where(cd => cd.Id == id && cd.IsActive)
            .FirstOrDefaultAsync();

        return document?.FileContent;
    }

    public Task<bool> ValidateFileAsync(byte[] fileContent, string fileName)
    {
        // Check if file is PDF
        if (!fileName.ToLowerInvariant().EndsWith(".pdf"))
            return Task.FromResult(false);

        // Check PDF signature (first 4 bytes should be %PDF)
        if (fileContent.Length < 4)
            return Task.FromResult(false);

        var pdfSignature = System.Text.Encoding.ASCII.GetString(fileContent, 0, 4);
        return Task.FromResult(pdfSignature == "%PDF");
    }

    public async Task<int> GetContractDocumentCountBySyndicationIdAsync(int syndicationId)
    {
        return await context.ContractDocuments
            .CountAsync(cd => cd.SyndicationId == syndicationId && cd.IsActive);
    }

    public async Task<List<ContractDocumentDto>> GetAllContractDocumentsAsync()
    {
        var documents = await context.ContractDocuments
            .Where(cd => cd.IsActive)
            .Include(cd => cd.Syndication)
            .ThenInclude(s => s.Program)
            .OrderByDescending(cd => cd.UploadedDate)
            .Select(cd => new ContractDocumentDto
            {
                Id = cd.Id,
                FileName = cd.FileName,
                SyndicationId = cd.SyndicationId,
                UploadedDate = cd.UploadedDate,
                UploadedBy = cd.UploadedBy,
                IsActive = cd.IsActive,
                SyndicationName = cd.Syndication.Program.ProgramName,
                FileSizeBytes = cd.FileContent != null ? cd.FileContent.Length : 0
            })
            .ToListAsync();

        return documents;
    }
}
