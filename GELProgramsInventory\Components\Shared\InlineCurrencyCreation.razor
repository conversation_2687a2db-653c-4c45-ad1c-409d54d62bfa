@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@inject CurrencyService CurrencyService

<SfDialog @ref="CurrencyDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Add New Currency</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="NewCurrency" OnValidSubmit="SaveCurrency" FormName="InlineCurrencyForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary />
                    
                    <div class="form-group mb-3">
                        <label>Currency Code</label>
                        <SfTextBox @bind-value="NewCurrency.Code" Placeholder="Enter currency code (e.g. USD)" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewCurrency.Code)" />
                    </div>
                    
                    <div class="form-group mb-3">
                        <label>Currency Name</label>
                        <SfTextBox @bind-value="NewCurrency.Name" Placeholder="Enter currency name (e.g. US Dollar)" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewCurrency.Name)" />
                    </div>
                    
                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card-body {
        padding: 1.5rem;
    }
    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>

@code {
    private SfDialog? CurrencyDialog;
    private CurrencyDto NewCurrency = new();

    [Parameter] public EventCallback<CurrencyDto> OnCurrencyCreated { get; set; }

    public async Task ShowDialog()
    {
        NewCurrency = new CurrencyDto { IsActive = true };
        if (CurrencyDialog != null)
        {
            await CurrencyDialog.ShowAsync();
        }
    }

    private async Task SaveCurrency()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(NewCurrency.Code) || string.IsNullOrWhiteSpace(NewCurrency.Name))
            {
                return;
            }

            var createdCurrency = await CurrencyService.CreateCurrencyAsync(NewCurrency);
            
            NewCurrency.CurrencyId = createdCurrency.CurrencyId;
            
            await OnCurrencyCreated.InvokeAsync(NewCurrency);
            await CloseDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating currency: {ex.Message}");
        }
    }

    private async Task CloseDialog()
    {
        if (CurrencyDialog != null)
        {
            await CurrencyDialog.HideAsync();
        }
        NewCurrency = new();
    }
}