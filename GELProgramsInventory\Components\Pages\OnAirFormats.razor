@page "/onair-formats"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Components.Shared
@inject OnAirFormatService OnAirFormatService
@rendermode InteractiveServer

<Breadcrumb CurrentPage="On Air Formats" />

<h1>On Air Formats</h1>

<SfButton OnClick="@OpenCreateDialog">Create New On Air Format</SfButton>
<SfDialog @ref="dlgOnAirFormat" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="600px" Visible="false" Height="400px">
    <DialogTemplates>
        <Header>On Air Format Information</Header>
        <Content>
            <EditForm Model="_selectedOnAirFormat" OnValidSubmit="SaveOnAirFormat" FormName="OnAirFormatForm">
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="On Air Format Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedOnAirFormat.OnAirFormatName"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md" style="display: flex; align-items: center; gap: 5px;" >
                        <span><b>Status</b></span> <SfSwitch @bind-Checked="_selectedOnAirFormat.IsActive"></SfSwitch> <span> @_selectedOnAirFormat.Status</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfButton CssClass="e-primary">Save</SfButton>
                        
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfGrid @ref="_grid" DataSource="@_onAirFormatsList" AllowPaging="true">
    <GridColumns>
        
        <GridColumn Field="@nameof(OnAirFormatDto.OnAirFormatName)" HeaderText="On Air Format Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(OnAirFormatDto.Status)" HeaderText="Status" AutoFit="true" ></GridColumn>
        
        <GridColumn HeaderText="Actions" Width="150">
            <Template Context="onAirFormatContext">
                <SfButton OnClick="@(() => OpenEditDialog((OnAirFormatDto)onAirFormatContext))">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteOnAirFormat(((OnAirFormatDto)onAirFormatContext).OnAirFormatId))">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private List<OnAirFormatDto> _onAirFormatsList = new();
    private SfGrid<OnAirFormatDto> _grid = new();
    private OnAirFormatDto _selectedOnAirFormat = new();
    private SfDialog? dlgOnAirFormat;

    
    protected override async Task OnInitializedAsync()
    {
        _onAirFormatsList = await OnAirFormatService.GetOnAirFormatDtosAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedOnAirFormat = new OnAirFormatDto() {IsActive = true};
        await dlgOnAirFormat!.ShowAsync();
    }

    private async Task OpenEditDialog(OnAirFormatDto onAirFormat)
    {
        _selectedOnAirFormat = onAirFormat;
        await dlgOnAirFormat!.ShowAsync();
    }

    private async Task SaveOnAirFormat()
    {
        if (_selectedOnAirFormat.OnAirFormatId == 0)
        {
            await OnAirFormatService.CreateOnAirFormatAsync(_selectedOnAirFormat);
        }
        else
        {
            await OnAirFormatService.UpdateOnAirFormatAsync(_selectedOnAirFormat);
        }

        _onAirFormatsList = await OnAirFormatService.GetOnAirFormatDtosAsync();
        await _grid.Refresh();
        await dlgOnAirFormat!.HideAsync();
    }

    private async Task DeleteOnAirFormat(int onAirFormatId)
    {
        await OnAirFormatService.DeleteOnAirFormatAsync(onAirFormatId);
        _onAirFormatsList = await OnAirFormatService.GetOnAirFormatDtosAsync();
        await _grid.Refresh();
    }

}
