@page "/programs"
@using GELProgramsInventory.Components.Shared
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Grids
@inject ProgramService ProgramService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Programs"/>

<div class="">
    
    <div style="display: flex; justify-content: space-between; align-items: flex-end;" class="mb-2 me-2">
        <div class="">
            <h1 class="mb-0">Programs Management</h1>
            <p class="page-description">Manage your television programs</p>
        </div>

        <div class="">
            <SfButton OnClick="NavigateToAddProgram" CssClass="e-primary e-large" aria-label="Add new program">
                <span class="e-btn-icon e-icons e-plus"></span>
                Add New Program
            </SfButton>
        </div>
    </div>



    <div class="">
        <SfGrid @ref="_programsGridRef" DataSource="@_programsData" AllowPaging="true" AllowSorting="true">
            <GridPageSettings PageSize="15"></GridPageSettings>
            <GridColumns>
                <GridColumn Field="PrimaryName" HeaderText="Program Name" Width="250"></GridColumn>
                <GridColumn Field="GenreName" HeaderText="Genre" Width="150"></GridColumn>
                <GridColumn Field="ProductionHouseName" HeaderText="Production House" Width="180"></GridColumn>
                <GridColumn Field="OnAirFormatName" HeaderText="On Air Format" Width="150"></GridColumn>
                <GridColumn Field="Duration" HeaderText="Duration (min)" Width="120"></GridColumn>
                <GridColumn Field="SyndicationDeliveryRemarks" HeaderText="Syndication Delivery Remarks" Width="200"></GridColumn>
                <GridColumn HeaderText="Actions" Width="120">
                    <Template>
                        @{
                            if (context is ProgramDto program)
                            {
                                <div class="action-buttons">
                                    <SfButton CssClass="e-small e-info" OnClick="@(() => EditProgram(program.ProgramId))" aria-label="Edit program">
                                        Edit
                                    </SfButton>
                                    <SfButton CssClass="e-small e-danger" OnClick="@(() => DeleteProgram(program.ProgramId))" aria-label="Delete program">
                                        Delete
                                    </SfButton>
                                </div>
                            }
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>

    <!-- Confirmation Dialog -->
    <SfDialog @ref="_confirmDialog" ShowCloseIcon="true" IsModal="true" Width="400px" Visible="false">
        <DialogTemplates>
            <Header>Confirm Delete</Header>
            <Content>
                <p>@_confirmMessage</p>
            </Content>
            <FooterTemplate>
                <SfButton OnClick="ConfirmDelete" CssClass="e-primary">Yes, Delete</SfButton>
                <SfButton OnClick="CancelDelete" CssClass="e-secondary">Cancel</SfButton>
            </FooterTemplate>
        </DialogTemplates>
    </SfDialog>
</div>


<style>
    .programs-container {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .page-header h1 {
        color: #333;
        margin-bottom: 10px;
    }

    .page-description {
        color: #666;
        margin: 0;
    }

    .toolbar-section {
        margin-bottom: 20px;
        display: flex;
        justify-content: flex-start;
        gap: 15px;
    }

    .grid-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        padding: 15px;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }

    .action-buttons .e-btn {
        min-width: auto;
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    @@media (max-width: 768px) {
        .programs-container {
            padding: 15px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 3px;
        }

        .toolbar-section {
            flex-direction: column;
            align-items: flex-start;
        }
    }
</style>

@code {
    private SfGrid<ProgramDto>? _programsGridRef;
    private SfDialog? _confirmDialog;
    private List<ProgramDto> _programsData = new();
    private string _confirmMessage = "";
    private int _itemToDelete;

    protected override async Task OnInitializedAsync()
    {
        await LoadProgramsData();
    }

    private async Task LoadProgramsData()
    {
        try
        {
            // Load programs
            _programsData = await ProgramService.GetProgramsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading program data: {ex.Message}");
        }
    }

    private void NavigateToAddProgram()
    {
        Navigation.NavigateTo("/programs/add");
    }

    private void EditProgram(int programId)
    {
        Navigation.NavigateTo($"/programs/edit/{programId}");
    }

    private async Task DeleteProgram(int programId)
    {
        _itemToDelete = programId;
        _confirmMessage = "Are you sure you want to delete this program? This action cannot be undone.";
        if (_confirmDialog != null)
        {
            await _confirmDialog.ShowAsync();
        }
    }

    private async Task ConfirmDelete()
    {
        try
        {
            await ProgramService.SoftDeleteProgramAsync(_itemToDelete);
            await LoadProgramsData();
            
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting program: {ex.Message}");
        }
        finally
        {
            await CancelDelete();
        }
    }

    private async Task CancelDelete()
    {
        if (_confirmDialog != null)
        {
            await _confirmDialog.HideAsync();
        }

        _itemToDelete = 0;
        _confirmMessage = "";
    }
}