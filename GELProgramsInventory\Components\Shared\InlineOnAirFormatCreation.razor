@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@inject OnAirFormatService OnAirFormatService

<SfDialog @ref="OnAirFormatDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Add New On Air Format</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="NewOnAirFormat" OnValidSubmit="SaveOnAirFormat" FormName="InlineOnAirFormatForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary />
                    
                    <div class="form-group mb-3">
                        <label>Format Name</label>
                        <SfTextBox @bind-value="NewOnAirFormat.OnAirFormatName" Placeholder="Enter on air format name" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewOnAirFormat.OnAirFormatName)" />
                    </div>
                    
                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card-body {
        padding: 1.5rem;
    }
    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>

@code {
    private SfDialog? OnAirFormatDialog;
    private OnAirFormatDto NewOnAirFormat = new();

    [Parameter] public EventCallback<OnAirFormatDto> OnOnAirFormatCreated { get; set; }

    public async Task ShowDialog()
    {
        NewOnAirFormat = new OnAirFormatDto { IsActive = true };
        if (OnAirFormatDialog != null)
        {
            await OnAirFormatDialog.ShowAsync();
        }
    }

    private async Task SaveOnAirFormat()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(NewOnAirFormat.OnAirFormatName))
            {
                return;
            }

            var createdFormat = await OnAirFormatService.CreateOnAirFormatAsync(NewOnAirFormat);
            
            NewOnAirFormat.OnAirFormatId = createdFormat.OnAirFormatId;
            
            await OnOnAirFormatCreated.InvokeAsync(NewOnAirFormat);
            await CloseDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating on air format: {ex.Message}");
        }
    }

    private async Task CloseDialog()
    {
        if (OnAirFormatDialog != null)
        {
            await OnAirFormatDialog.HideAsync();
        }
        NewOnAirFormat = new();
    }
}