@page "/admin/users"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Components.Shared
@inject GELProgramsInventory.Services.UserService UserService
@inject GELProgramsInventory.Services.RoleService RoleService
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Admin" SubPage="Users" />

<div style="display: flex; justify-content: space-between; align-items: flex-end;" class="mb-2 me-2">
    <div class="">
        <h1 class="mb-0">Users Management</h1>
        <p class="page-description">Manage application users and their role assignments</p>
    </div>

    <div class="">
        <SfButton OnClick="OpenCreateDialog" CssClass="e-primary e-large" aria-label="Add new user">
            <span class="e-btn-icon e-icons e-plus"></span>
            Add New User
        </SfButton>
    </div>
</div>

<SfGrid @ref="_grid" DataSource="@_usersList" AllowPaging="true" AllowSorting="true">
    <GridColumns>
        <GridColumn Field="@nameof(UserDto.UserId)" HeaderText="User ID" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(UserDto.FullName)" HeaderText="Full Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(UserDto.EmployeeCode)" HeaderText="Employee Code" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(UserDto.Status)" HeaderText="Status" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(UserDto.CreatedDate)" HeaderText="Created Date" Format="dd/MM/yyyy" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Assigned Roles" AutoFit="true">
            <Template Context="userContext">
                @{
                    var user = (UserDto)userContext;
                    var roleTitles = user.AssignedRoles.Select(r => r.RoleTitle).ToList();
                }
                @string.Join(", ", roleTitles)
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Actions" Width="200">
            <Template Context="userContext">
                <SfButton OnClick="@(() => OpenEditDialog((UserDto)userContext))" CssClass="e-info">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteUser(((UserDto)userContext).UserId!))" CssClass="e-danger">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dlgUser" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="800px" Visible="false">
    <DialogTemplates>
        <Header>User Information</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="_selectedUser" OnValidSubmit="SaveUser" FormName="UserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>User ID</label>
                                <SfTextBox Placeholder="domain\\username" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedUser.UserId" Enabled="@(_selectedUser.UserId == null)"></SfTextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>Full Name</label>
                                <SfTextBox Placeholder="Full Name" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedUser.FullName"></SfTextBox>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>Employee Code</label>
                                <SfTextBox Placeholder="Employee Code" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedUser.EmployeeCode"></SfTextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>Status</label>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <SfSwitch @bind-Checked="_selectedUser.IsActive"></SfSwitch> 
                                    <span>@_selectedUser.Status</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label>Assigned Roles</label>
                        <SfMultiSelect TValue="int[]" TItem="RoleDto" 
                                      DataSource="@_rolesList" 
                                      @bind-Value="_selectedRoleIds"
                                      Placeholder="Select roles to assign"
                                      Mode="VisualMode.CheckBox">
                            <MultiSelectFieldSettings Text="RoleTitle" Value="RoleId"></MultiSelectFieldSettings>
                            <MultiSelectTemplates TItem="RoleDto">
                                <ItemTemplate Context="roleItem">
                                    <div>
                                        @roleItem.RoleTitle
                                        <span class="badge @(roleItem.RoleIsActive ? "badge-success" : "badge-secondary") ms-1">
                                            @roleItem.Status
                                        </span>
                                    </div>
                                </ItemTemplate>
                            </MultiSelectTemplates>
                        </SfMultiSelect>
                    </div>

                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<UserDto> _usersList = new();
    private List<RoleDto> _rolesList = new();
    private SfGrid<UserDto> _grid = new();
    private UserDto _selectedUser = new();
    private SfDialog? dlgUser;
    private int[] _selectedRoleIds = Array.Empty<int>();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _usersList = await UserService.GetUsersAsync();
        _rolesList = await RoleService.GetRolesAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedUser = new UserDto() { IsActive = true, CreatedBy = "system", ModifiedBy = "system" };
        _selectedRoleIds = Array.Empty<int>();
        if (dlgUser != null)
        {
            await dlgUser.ShowAsync();
        }
    }

    private async Task OpenEditDialog(UserDto user)
    {
        _selectedUser = user;
        _selectedRoleIds = user.AssignedRoleIds.ToArray();
        if (dlgUser != null)
        {
            await dlgUser.ShowAsync();
        }
    }

    private async Task SaveUser()
    {
        _selectedUser.AssignedRoleIds = _selectedRoleIds.ToList();
        
        if (string.IsNullOrEmpty(_selectedUser.UserId) || _usersList.All(u => u.UserId != _selectedUser.UserId))
        {
            await UserService.CreateUserAsync(_selectedUser);
        }
        else
        {
            await UserService.UpdateUserAsync(_selectedUser);
        }

        await LoadData();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
        await CloseDialog();
    }

    private async Task DeleteUser(string userId)
    {
        await UserService.DeleteUserAsync(userId);
        await LoadData();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
    }

    private async Task CloseDialog()
    {
        if (dlgUser != null)
        {
            await dlgUser.HideAsync();
        }
    }
}
