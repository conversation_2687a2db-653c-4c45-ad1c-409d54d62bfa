using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class RoleService(ApplicationDbContext context)
{
    public Task<List<RoleDto>> GetRolesAsync()
    {
        var roles = context.Roles
            .Include(r => r.RoleMenus)
            .ThenInclude(rm => rm.Menu)
            .Select(r => new RoleDto
            {
                RoleId = r.RoleId,
                RoleTitle = r.RoleTitle,
                RoleIsActive = r.RoleIsActive,
                CreatedDate = r.CreatedDate,
                ModifiedDate = r.ModifiedDate,
                CreatedBy = r.CreatedBy,
                ModifiedBy = r.ModifiedBy,
                AssignedMenuIds = r.RoleMenus.Select(rm => rm.MenuId).ToList(),
                AssignedMenus = r.RoleMenus.Select(rm => new MenuDto
                {
                    MenuId = rm.Menu.MenuId,
                    MemuTitle = rm.Menu.MemuTitle,
                    MenuLink = rm.Menu.MenuLink,
                    Module = rm.Menu.Module,
                    Icon = rm.Menu.Icon,
                    SortOrder = rm.Menu.SortOrder
                }).ToList()
            })
            .OrderBy(r => r.RoleTitle)
            .ToList();

        return Task.FromResult(roles);
    }

    public Task<RoleDto?> GetRoleByIdAsync(int id)
    {
        var role = context.Roles
            .Include(r => r.RoleMenus)
            .ThenInclude(rm => rm.Menu)
            .Where(r => r.RoleId == id)
            .Select(r => new RoleDto
            {
                RoleId = r.RoleId,
                RoleTitle = r.RoleTitle,
                RoleIsActive = r.RoleIsActive,
                CreatedDate = r.CreatedDate,
                ModifiedDate = r.ModifiedDate,
                CreatedBy = r.CreatedBy,
                ModifiedBy = r.ModifiedBy,
                AssignedMenuIds = r.RoleMenus.Select(rm => rm.MenuId).ToList(),
                AssignedMenus = r.RoleMenus.Select(rm => new MenuDto
                {
                    MenuId = rm.Menu.MenuId,
                    MemuTitle = rm.Menu.MemuTitle,
                    MenuLink = rm.Menu.MenuLink,
                    Module = rm.Menu.Module,
                    Icon = rm.Menu.Icon,
                    SortOrder = rm.Menu.SortOrder
                }).ToList()
            })
            .FirstOrDefault();

        return Task.FromResult(role);
    }

    public Task<RoleDto> CreateRoleAsync(RoleDto roleDto)
    {
        var role = new Role
        {
            RoleTitle = roleDto.RoleTitle,
            RoleIsActive = roleDto.RoleIsActive,
            CreatedDate = DateTime.Now,
            ModifiedDate = DateTime.Now,
            CreatedBy = roleDto.CreatedBy,
            ModifiedBy = roleDto.ModifiedBy
        };

        context.Roles.Add(role);
        context.SaveChanges();

        // Assign menus to role
        if (roleDto.AssignedMenuIds.Any())
        {
            var roleMenus = roleDto.AssignedMenuIds.Select(menuId => new RoleMenu
            {
                RoleId = role.RoleId,
                MenuId = menuId,
                CreatedDate = DateTime.Now,
                CreatedBy = roleDto.CreatedBy
            }).ToList();

            context.RoleMenus.AddRange(roleMenus);
            context.SaveChanges();
        }

        roleDto.RoleId = role.RoleId;
        return Task.FromResult(roleDto);
    }

    public Task<bool> UpdateRoleAsync(RoleDto roleDto)
    {
        var role = context.Roles.Find(roleDto.RoleId);
        if (role == null)
        {
            return Task.FromResult(false);
        }

        role.RoleTitle = roleDto.RoleTitle;
        role.RoleIsActive = roleDto.RoleIsActive;
        role.ModifiedDate = DateTime.Now;
        role.ModifiedBy = roleDto.ModifiedBy;

        // Update role menus
        var existingRoleMenus = context.RoleMenus.Where(rm => rm.RoleId == roleDto.RoleId).ToList();
        context.RoleMenus.RemoveRange(existingRoleMenus);

        if (roleDto.AssignedMenuIds.Any())
        {
            var newRoleMenus = roleDto.AssignedMenuIds.Select(menuId => new RoleMenu
            {
                RoleId = roleDto.RoleId,
                MenuId = menuId,
                CreatedDate = DateTime.Now,
                CreatedBy = roleDto.ModifiedBy
            }).ToList();

            context.RoleMenus.AddRange(newRoleMenus);
        }

        context.SaveChanges();
        return Task.FromResult(true);
    }

    public Task<bool> DeleteRoleAsync(int id)
    {
        var role = context.Roles.Find(id);
        if (role == null)
        {
            return Task.FromResult(false);
        }

        // Remove role menus first
        var roleMenus = context.RoleMenus.Where(rm => rm.RoleId == id).ToList();
        context.RoleMenus.RemoveRange(roleMenus);

        // Remove user roles
        var userRoles = context.UserRoles.Where(ur => ur.RoleId == id).ToList();
        context.UserRoles.RemoveRange(userRoles);

        context.Roles.Remove(role);
        context.SaveChanges();
        return Task.FromResult(true);
    }

    public Task<List<RoleMenuDto>> GetRoleMenusAsync(int roleId)
    {
        var roleMenus = context.RoleMenus
            .Include(rm => rm.Role)
            .Include(rm => rm.Menu)
            .Where(rm => rm.RoleId == roleId)
            .Select(rm => new RoleMenuDto
            {
                RoleId = rm.RoleId,
                MenuId = rm.MenuId,
                CreatedDate = rm.CreatedDate,
                CreatedBy = rm.CreatedBy,
                RoleTitle = rm.Role.RoleTitle,
                MenuTitle = rm.Menu.MemuTitle
            })
            .ToList();

        return Task.FromResult(roleMenus);
    }
}
