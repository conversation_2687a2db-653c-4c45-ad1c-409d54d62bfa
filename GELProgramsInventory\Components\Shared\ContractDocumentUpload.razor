@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Microsoft.AspNetCore.Components.Forms
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Popups
@inject ContractDocumentService ContractDocumentService
@inject IJSRuntime JSRuntime

<div class="contract-documents-section">
    <div class="section-header">
        <h5 class="section-title">Contract Documents</h5>
        <SfButton CssClass="e-small e-success" OnClick="ShowUploadDialog" type="button">
            <span class="e-btn-icon e-icons e-plus"></span>
            Upload PDF
        </SfButton>
    </div>

    @if (ContractDocuments.Any())
    {
        <div class="documents-grid">
            <SfGrid DataSource="ContractDocuments" AllowPaging="false" Height="300">
                <GridColumns>
                    <GridColumn Field="FileName" HeaderText="File Name" Width="200"></GridColumn>
                    <GridColumn Field="FileSizeFormatted" HeaderText="Size" Width="80"></GridColumn>
                    <GridColumn Field="UploadedDate" HeaderText="Uploaded" Width="120" Format="d" Type="ColumnType.Date"></GridColumn>
                    <GridColumn Field="UploadedBy" HeaderText="Uploaded By" Width="120"></GridColumn>
                    <GridColumn HeaderText="Actions" Width="150">
                        <Template>
                            @{
                                if (context is ContractDocumentDto document)
                                {
                                    <div class="action-buttons">
                                        <SfButton CssClass="e-small e-info" OnClick="@(() => PreviewDocument(document.Id))" aria-label="Preview document" type="button">
                                            Preview
                                        </SfButton>
                                        <SfButton CssClass="e-small e-success" OnClick="@(() => DownloadDocument(document.Id, document.FileName))" aria-label="Download document" type="button">
                                            Download
                                        </SfButton>
                                        <SfButton CssClass="e-small e-danger" OnClick="@(() => DeleteDocument(document.Id))" aria-label="Delete document" type="button">
                                            Delete
                                        </SfButton>
                                    </div>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
    else
    {
        <div class="no-documents">
            <p>No contract documents uploaded yet.</p>
        </div>
    }
</div>

<!-- Upload Dialog -->
<SfDialog @ref="UploadDialog" ShowCloseIcon="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Upload Contract Document</Header>
        <Content>
            <div class="upload-content">
                <EditForm Model="NewDocument" OnValidSubmit="UploadDocument" FormName="ContractUploadForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary class="validation-summary" />

                    <div class="form-group mb-3">
                        <label>Select PDF File (Max 10MB)</label>
                        <InputFile OnChange="HandleFileSelected" accept=".pdf" class="form-control" />
                        @if (!string.IsNullOrEmpty(FileValidationMessage))
                        {
                            <div class="text-danger mt-1">@FileValidationMessage</div>
                        }
                    </div>

                    @if (SelectedFile != null)
                    {
                        <div class="file-info">
                            <strong>Selected File:</strong> @SelectedFile.Name<br />
                            <strong>Size:</strong> @FormatFileSize(SelectedFile.Size)
                        </div>
                    }

                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" Disabled="@(SelectedFile == null || IsUploading)">
                            @if (IsUploading)
                            {
                                <span>Uploading...</span>
                            }
                            else
                            {
                                <span>Upload</span>
                            }
                        </SfButton>
                        <SfButton OnClick="CancelUpload" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Preview Dialog -->
<SfDialog @ref="PreviewDialog" ShowCloseIcon="true" IsModal="true" Width="80%" Height="80%" Visible="false">
    <DialogTemplates>
        <Header>Document Preview - @PreviewFileName</Header>
        <Content>
            <div class="preview-content">
                @if (!string.IsNullOrEmpty(PreviewUrl))
                {
                    <iframe src="@PreviewUrl" width="100%" height="600px" style="border: none;"></iframe>
                }
                else
                {
                    <p>Loading preview...</p>
                }
            </div>
        </Content>
        <FooterTemplate>
            <SfButton OnClick="ClosePreview" CssClass="e-secondary">Close</SfButton>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>

<style>
    .contract-documents-section {
        margin-top: 1rem;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .documents-grid {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
    }

    .no-documents {
        text-align: center;
        padding: 2rem;
        color: #666;
        border: 2px dashed #ddd;
        border-radius: 4px;
    }

    .upload-content {
        padding: 1rem;
    }

    .file-info {
        background-color: #f8f9fa;
        padding: 0.75rem;
        border-radius: 4px;
        margin: 1rem 0;
        border-left: 4px solid #007bff;
    }

    .form-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        margin-top: 1rem;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .action-buttons .e-btn {
        min-width: auto;
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    .validation-summary {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 4px;
        margin-bottom: 1rem;
    }

    .preview-content {
        height: 100%;
    }
</style>

@code {
    [Parameter] public int SyndicationId { get; set; }
    [Parameter] public EventCallback<ContractDocumentDto> OnDocumentUploaded { get; set; }
    [Parameter] public EventCallback<Guid> OnDocumentDeleted { get; set; }

    private SfDialog? UploadDialog;
    private SfDialog? PreviewDialog;
    private List<ContractDocumentDto> ContractDocuments = new();
    private ContractDocumentDto NewDocument = new();
    private IBrowserFile? SelectedFile;
    private string FileValidationMessage = string.Empty;
    private bool IsUploading = false;
    private string PreviewUrl = string.Empty;
    private string PreviewFileName = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadContractDocuments();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (SyndicationId > 0)
        {
            await LoadContractDocuments();
        }
    }

    private async Task LoadContractDocuments()
    {
        if (SyndicationId > 0)
        {
            ContractDocuments = await ContractDocumentService.GetContractDocumentsBySyndicationIdAsync(SyndicationId);
            StateHasChanged();
        }
    }

    private async Task ShowUploadDialog()
    {
        NewDocument = new ContractDocumentDto { SyndicationId = SyndicationId };
        SelectedFile = null;
        FileValidationMessage = string.Empty;
        if (UploadDialog != null)
        {
            await UploadDialog.ShowAsync();
        }
    }

    private void HandleFileSelected(InputFileChangeEventArgs e)
    {
        SelectedFile = e.File;
        FileValidationMessage = string.Empty;

        if (SelectedFile != null)
        {
            // Validate file type
            if (!SelectedFile.Name.ToLowerInvariant().EndsWith(".pdf"))
            {
                FileValidationMessage = "Only PDF files are allowed.";
                SelectedFile = null;
                return;
            }

            // Validate file size (10MB limit)
            if (SelectedFile.Size > 10 * 1024 * 1024)
            {
                FileValidationMessage = "File size cannot exceed 10MB.";
                SelectedFile = null;
                return;
            }

            NewDocument.FileName = SelectedFile.Name;
        }
    }

    private async Task UploadDocument()
    {
        if (SelectedFile == null) return;

        IsUploading = true;
        try
        {
            // Read file content
            using var stream = SelectedFile.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024);
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            var fileContent = memoryStream.ToArray();

            // Validate PDF content
            if (!await ContractDocumentService.ValidateFileAsync(fileContent, SelectedFile.Name))
            {
                FileValidationMessage = "Invalid PDF file format.";
                return;
            }

            NewDocument.FileContent = fileContent;
            NewDocument.SyndicationId = SyndicationId;

            // Upload document
            var uploadedDocument = await ContractDocumentService.CreateContractDocumentAsync(NewDocument, "Current User");

            // Refresh the list
            await LoadContractDocuments();

            // Notify parent component
            await OnDocumentUploaded.InvokeAsync(uploadedDocument);

            // Close dialog
            await CancelUpload();
        }
        catch (Exception ex)
        {
            FileValidationMessage = $"Upload failed: {ex.Message}";
        }
        finally
        {
            IsUploading = false;
        }
    }

    private async Task CancelUpload()
    {
        if (UploadDialog != null)
        {
            await UploadDialog.HideAsync();
        }
        NewDocument = new();
        SelectedFile = null;
        FileValidationMessage = string.Empty;
        IsUploading = false;
    }

    private async Task PreviewDocument(Guid documentId)
    {
        try
        {
            var document = await ContractDocumentService.GetContractDocumentByIdAsync(documentId);
            if (document?.FileContent != null)
            {
                PreviewFileName = document.FileName;

                // Create blob URL for PDF preview
                var base64 = Convert.ToBase64String(document.FileContent);
                PreviewUrl = $"data:application/pdf;base64,{base64}";

                if (PreviewDialog != null)
                {
                    await PreviewDialog.ShowAsync();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error previewing document: {ex.Message}");
        }
    }

    private async Task ClosePreview()
    {
        if (PreviewDialog != null)
        {
            await PreviewDialog.HideAsync();
        }
        PreviewUrl = string.Empty;
        PreviewFileName = string.Empty;
    }

    private async Task DeleteDocument(Guid documentId)
    {
        try
        {
            var success = await ContractDocumentService.DeleteContractDocumentAsync(documentId);
            if (success)
            {
                await LoadContractDocuments();
                await OnDocumentDeleted.InvokeAsync(documentId);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting document: {ex.Message}");
        }
    }

    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }

    private async Task DownloadDocument(Guid documentId, string fileName)
    {
        try
        {
            var fileContent = await ContractDocumentService.GetFileContentAsync(documentId);
            if (fileContent != null)
            {
                // Create blob URL and trigger download
                var base64 = Convert.ToBase64String(fileContent);
                var dataUrl = $"data:application/pdf;base64,{base64}";

                await JSRuntime.InvokeVoidAsync("downloadFile", dataUrl, fileName);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error downloading document: {ex.Message}");
        }
    }

    public async Task RefreshDocuments()
    {
        await LoadContractDocuments();
    }
}
