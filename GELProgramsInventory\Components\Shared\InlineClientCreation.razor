@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@inject ClientService ClientService

<SfDialog @ref="ClientDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Add New Client</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="NewClient" OnValidSubmit="SaveClient" FormName="InlineClientForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary />
                    
                    <div class="form-group mb-3">
                        <label>Client Name</label>
                        <SfTextBox @bind-value="NewClient.ClientName" Placeholder="Enter client name" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewClient.ClientName)" />
                    </div>

                    <div class="form-group mb-3">
                        <label>Contact Information</label>
                        <SfTextBox @bind-value="NewClient.ContactInfo" Placeholder="Enter contact info (optional)" FloatLabelType="FloatLabelType.Never" Multiline="true" Rows="3"></SfTextBox>
                    </div>
                    
                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card-body {
        padding: 1.5rem;
    }
    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>

@code {
    private SfDialog? ClientDialog;
    private ClientDto NewClient = new();

    [Parameter] public EventCallback<ClientDto> OnClientCreated { get; set; }

    public async Task ShowDialog()
    {
        NewClient = new ClientDto { IsActive = true };
        if (ClientDialog != null)
        {
            await ClientDialog.ShowAsync();
        }
    }

    private async Task SaveClient()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(NewClient.ClientName))
            {
                return;
            }

            var createdClient = await ClientService.CreateClientAsync(NewClient);
            
            NewClient.ClientId = createdClient.ClientId;
            
            await OnClientCreated.InvokeAsync(NewClient);
            await CloseDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating client: {ex.Message}");
        }
    }

    private async Task CloseDialog()
    {
        if (ClientDialog != null)
        {
            await ClientDialog.HideAsync();
        }
        NewClient = new();
    }
}
