namespace GELProgramsInventory.Models.Dtos;

public class MenuDto
{
    public int MenuId { get; set; }
    public string? MemuTitle { get; set; }
    public string? MenuLink { get; set; }
    public int SortOrder { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime ModifiedDate { get; set; }
    public string? CreatedBy { get; set; }
    public string? ModifiedBy { get; set; }
    public string? Module { get; set; }
    public string? Icon { get; set; }
    public List<int> AssignedRoleIds { get; set; } = new();
    public List<RoleDto> AssignedRoles { get; set; } = new();
}
