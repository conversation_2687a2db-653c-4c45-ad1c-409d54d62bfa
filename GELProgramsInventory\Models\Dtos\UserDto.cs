namespace GELProgramsInventory.Models.Dtos;

public class UserDto
{
    public string? UserId { get; set; }
    public string? FullName { get; set; }
    public string? EmployeeCode { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime ModifiedDate { get; set; }
    public string? CreatedBy { get; set; }
    public string? ModifiedBy { get; set; }
    public string Status => IsActive ? "Active" : "Inactive";
    public List<int> AssignedRoleIds { get; set; } = new();
    public List<RoleDto> AssignedRoles { get; set; } = new();
}

public class UserRoleDto
{
    public string? UserId { get; set; }
    public int RoleId { get; set; }
    public DateTime CreatedDate { get; set; }
    public string? CreatedBy { get; set; }
    public string? UserFullName { get; set; }
    public string? RoleTitle { get; set; }
}
