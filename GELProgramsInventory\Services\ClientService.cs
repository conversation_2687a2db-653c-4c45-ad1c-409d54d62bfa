using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;

namespace GELProgramsInventory.Services;

public class ClientService(ApplicationDbContext context)
{
    public Task<List<ClientDto>> GetClientsAsync()
    {
        var res = context.Clients
            .Select(c => new ClientDto
            {
                ClientId = c.ClientId,
                ClientName = c.ClientName,
                ContactInfo = c.ContactInfo,
                IsActive = c.IsActive
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<ClientDto?> GetClientByIdAsync(int id)
    {
        var client = context.Clients.Find(id);
        if (client == null) return null!;

        var res = new ClientDto
        {
            ClientId = client.ClientId,
            ClientName = client.ClientName,
            ContactInfo = client.ContactInfo,
            IsActive = client.IsActive
        };
        return Task.FromResult<ClientDto?>(res);
    }

    public Task<Client> CreateClientAsync(ClientDto clientDto)
    {
        var client = new Client
        {
            ClientName = clientDto.ClientName,
            ContactInfo = clientDto.ContactInfo,
            IsActive = clientDto.IsActive,
            CreatedDate = DateTime.Now
        };

        context.Clients.Add(client);
        context.SaveChanges();
        return Task.FromResult(client);
    }

    public Task UpdateClientAsync(ClientDto clientDto)
    {
        var client = context.Clients.Find(clientDto.ClientId);
        if (client != null)
        {
            client.ClientName = clientDto.ClientName;
            client.ContactInfo = clientDto.ContactInfo;
            client.IsActive = clientDto.IsActive;
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    public Task DeleteClientAsync(int id)
    {
        var client = context.Clients.Find(id);
        if (client != null)
        {
            context.Clients.Remove(client);
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }
}