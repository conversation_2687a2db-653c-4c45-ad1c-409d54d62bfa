using System.ComponentModel.DataAnnotations;

namespace GELProgramsInventory.Models.Dtos;

public class SyndicationDto
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Program is required")]
    public int? ProgramId { get; set; }
    public string? ProgramName { get; set; }

    [Required(ErrorMessage = "Client is required")]
    public int? ClientId { get; set; }
    public string? ClientName { get; set; }

    [Required(ErrorMessage = "Syndication format is required")]
    public int? SyndicationFormatId { get; set; }
    public string? SyndicationFormatName { get; set; }

    [Required(ErrorMessage = "Language is required")]
    public int? LanguageId { get; set; }
    public string? LanguageName { get; set; }
    
    // New fields
    public int? OnAirFormatId { get; set; }
    public string? OnAirFormatName { get; set; }
    
    public DateTime? ContractStartDate { get; set; }
    public DateTime? ContractEndDate { get; set; }
    
    public int? TerritoryId { get; set; }
    public string? TerritoryName { get; set; }
    
    public int? EpisodeFrom { get; set; }
    public int? EpisodeTo { get; set; }
    
    public decimal? PricePerEpisode { get; set; }
    
    public int? CurrencyId { get; set; }
    public string? CurrencyCode { get; set; }

    [StringLength(1000, ErrorMessage = "Remarks cannot exceed 1000 characters")]
    public string? Remarks { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
}
