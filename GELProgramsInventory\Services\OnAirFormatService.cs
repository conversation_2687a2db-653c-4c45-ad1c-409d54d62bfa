using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;

namespace GELProgramsInventory.Services;

public class OnAirFormatService(ApplicationDbContext context)
{
    public Task<List<OnAirFormat>> GetOnAirFormatsAsync()
    {
        var res = context.OnAirFormats.ToList();
        return Task.FromResult(res);
    }

    public Task<List<OnAirFormatDto>> GetOnAirFormatDtosAsync()
    {
        var res = context.OnAirFormats
            .Select(oaf => new OnAirFormatDto
            {
                OnAirFormatId = oaf.OnAirFormatId,
                OnAirFormatName = oaf.FormatName,
                IsActive = oaf.IsActive
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<OnAirFormatDto?> GetOnAirFormatByIdAsync(int id)
    {
        var onAirFormat = context.OnAirFormats.Find(id);
        if (onAirFormat == null)
        {
            return Task.FromResult<OnAirFormatDto?>(null);
        }

        var res = new OnAirFormatDto
        {
            OnAirFormatId = onAirFormat.OnAirFormatId,
            OnAirFormatName = onAirFormat.FormatName,
            IsActive = onAirFormat.IsActive
        };
        return Task.FromResult<OnAirFormatDto?>(res);
    }

    public Task<OnAirFormat> CreateOnAirFormatAsync(OnAirFormatDto onAirFormatDto)
    {
        var onAirFormat = new OnAirFormat
        {
            FormatName = onAirFormatDto.OnAirFormatName,
            IsActive = onAirFormatDto.IsActive,
            CreatedDate = DateTime.Now
        };

        context.OnAirFormats.Add(onAirFormat);
        context.SaveChanges();
        return Task.FromResult(onAirFormat);
    }

    public Task UpdateOnAirFormatAsync(OnAirFormatDto onAirFormatDto)
    {
        var onAirFormat = context.OnAirFormats.Find(onAirFormatDto.OnAirFormatId);
        if (onAirFormat != null)
        {
            onAirFormat.FormatName = onAirFormatDto.OnAirFormatName;
            onAirFormat.IsActive = onAirFormatDto.IsActive;
            context.SaveChanges();
        }
        return Task.CompletedTask;
    }

    public Task DeleteOnAirFormatAsync(int id)
    {
        var onAirFormat = context.OnAirFormats.Find(id);
        if (onAirFormat != null)
        {
            context.OnAirFormats.Remove(onAirFormat);
            context.SaveChanges();
        }
        return Task.CompletedTask;
    }
}
