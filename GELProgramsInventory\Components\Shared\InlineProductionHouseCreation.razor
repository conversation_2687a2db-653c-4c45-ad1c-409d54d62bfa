@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@inject ProductionHouseService ProductionHouseService

<SfDialog @ref="ProductionHouseDialog" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="500px" Visible="false">
    <DialogTemplates>
        <Header>Add New Production House</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="NewProductionHouse" OnValidSubmit="SaveProductionHouse" FormName="InlineProductionHouseForm">
                    <DataAnnotationsValidator />
                    <ValidationSummary />
                    
                    <div class="form-group mb-3">
                        <label>Production House Name</label>
                        <SfTextBox @bind-value="NewProductionHouse.ProductionHouseName" Placeholder="Enter production house name" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        <ValidationMessage For="@(() => NewProductionHouse.ProductionHouseName)" />
                    </div>

                    <div class="form-group mb-3">
                        <label>Contact Information</label>
                        <SfTextBox @bind-value="NewProductionHouse.ContactInfo" Placeholder="Enter contact info (optional)" FloatLabelType="FloatLabelType.Never" Multiline="true" Rows="3"></SfTextBox>
                    </div>
                    
                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card-body {
        padding: 1.5rem;
    }
    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>

@code {
    private SfDialog? ProductionHouseDialog;
    private ProductionHouseDto NewProductionHouse = new();

    [Parameter] public EventCallback<ProductionHouseDto> OnProductionHouseCreated { get; set; }

    public async Task ShowDialog()
    {
        NewProductionHouse = new ProductionHouseDto { IsActive = true };
        if (ProductionHouseDialog != null)
        {
            await ProductionHouseDialog.ShowAsync();
        }
    }

    private async Task SaveProductionHouse()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(NewProductionHouse.ProductionHouseName))
            {
                return;
            }

            var createdProductionHouse = await ProductionHouseService.CreateProductionHouseAsync(NewProductionHouse);
            
            NewProductionHouse.ProductionHouseId = createdProductionHouse.ProductionHouseId;
            
            await OnProductionHouseCreated.InvokeAsync(NewProductionHouse);
            await CloseDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating production house: {ex.Message}");
        }
    }

    private async Task CloseDialog()
    {
        if (ProductionHouseDialog != null)
        {
            await ProductionHouseDialog.HideAsync();
        }
        NewProductionHouse = new();
    }
}