@using Microsoft.FluentUI.AspNetCore.Components.Icons
@inject NavigationManager Navigation

<div class="breadcrumb-container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/" class="breadcrumb-link">
                    <FluentIcon Icon="Icons.Regular.Size16.Home" />
                    Home
                </a>
            </li>
            @if (!string.IsNullOrEmpty(CurrentPage))
            {
                <li class="breadcrumb-item active" aria-current="page">
                    @CurrentPage
                </li>
            }
            @if (!string.IsNullOrEmpty(SubPage))
            {
                <li class="breadcrumb-item active" aria-current="page">
                    @SubPage
                </li>
            }
        </ol>
    </nav>
</div>

<style>
    .breadcrumb-container {
        margin-bottom: 20px;
        padding: 10px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .breadcrumb {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        margin: 0;
        list-style: none;
        background-color: transparent;
    }

    .breadcrumb-item {
        display: flex;
        align-items: center;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        padding: 0 8px;
        color: #6c757d;
    }

    .breadcrumb-link {
        color: #0d6efd;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 5px 10px;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

    .breadcrumb-link:hover {
        background-color: #f8f9fa;
        text-decoration: none;
    }

    .breadcrumb-item.active {
        color: #6c757d;
        font-weight: 500;
        padding: 5px 10px;
    }

    @@media (max-width: 768px) {
        .breadcrumb {
            font-size: 0.9rem;
        }
        
        .breadcrumb-link {
            padding: 3px 6px;
        }
    }
</style>

@code {
    [Parameter] public string? CurrentPage { get; set; }
    [Parameter] public string? SubPage { get; set; }
}
