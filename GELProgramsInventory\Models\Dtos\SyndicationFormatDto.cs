using System.ComponentModel.DataAnnotations;

namespace GELProgramsInventory.Models.Dtos;

public class SyndicationFormatDto
{
    public int SyndicationFormatId { get; set; }

    [Required(ErrorMessage = "Format name is required")]
    [StringLength(100, ErrorMessage = "Format name cannot exceed 100 characters")]
    public string? FormatName { get; set; }

    public string? SyndicationFormatName { get; set; }

    public bool? IsActive { get; set; }
    public string Status => IsActive == true ? "Active" : "Inactive";
}
