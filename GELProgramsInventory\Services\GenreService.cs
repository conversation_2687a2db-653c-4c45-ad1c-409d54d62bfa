using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;

namespace GELProgramsInventory.Services;

public class GenreService(ApplicationDbContext context)
{
    public Task<List<Genre>> GetGenresAsync()
    {
        var res = context.Genres.ToList();
        return Task.FromResult(res);
    }

    public Task<List<GenreDto>> GetGenreDtosAsync()
    {
        var res = context.Genres
            .Select(g => new GenreDto
            {
                GenreId = g.GenreId,
                GenreName = g.GenreName,
                IsActive = g.IsActive
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<GenreDto?> GetGenreByIdAsync(int id)
    {
        var genre = context.Genres.Find(id);
        if (genre == null) return Task.FromResult<GenreDto?>(null);

        var res = new GenreDto
        {
            GenreId = genre.GenreId,
            GenreName = genre.GenreName,
            IsActive = genre.IsActive
        };
        return Task.FromResult<GenreDto?>(res);
    }

    public Task<Genre> CreateGenreAsync(GenreDto genreDto)
    {
        var genre = new Genre
        {
            GenreName = genreDto.GenreName,
            IsActive = genreDto.IsActive,
            CreatedDate = DateTime.Now
        };

        context.Genres.Add(genre);
        context.SaveChanges();
        return Task.FromResult(genre);
    }

    public Task UpdateGenreAsync(GenreDto genreDto)
    {
        var genre = context.Genres.Find(genreDto.GenreId);
        if (genre != null)
        {
            genre.GenreName = genreDto.GenreName;
            genre.IsActive = genreDto.IsActive;
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    public Task DeleteGenreAsync(int id)
    {
        var genre = context.Genres.Find(id);
        if (genre != null)
        {
            context.Genres.Remove(genre);
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }
}