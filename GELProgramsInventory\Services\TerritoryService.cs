using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;
using Microsoft.EntityFrameworkCore;

namespace GELProgramsInventory.Services;

public class TerritoryService(ApplicationDbContext context)
{
    public Task<List<TerritoryDto>> GetTerritoriesAsync()
    {
        var territories = context.Territories
            .OrderBy(t => t.Title)
            .Select(t => new TerritoryDto
            {
                TerritoryId = t.TerritoryId,
                Name = t.Title,
                IsActive = true
            })
            .ToList();

        return Task.FromResult(territories);
    }

    public Task<TerritoryDto?> GetTerritoryByIdAsync(int id)
    {
        var territory = context.Territories
            .Where(t => t.TerritoryId == id)
            .Select(t => new TerritoryDto
            {
                TerritoryId = t.TerritoryId,
                Name = t.Title,
                IsActive = true
            })
            .FirstOrDefault();

        return Task.FromResult(territory);
    }

    public Task<TerritoryDto> CreateTerritoryAsync(TerritoryDto territoryDto)
    {
        var territory = new Territory
        {
            Title = territoryDto.Name,
            CreateDate = DateTime.Now,
            ModifiedDate = DateTime.Now
        };

        context.Territories.Add(territory);
        context.SaveChanges();

        territoryDto.TerritoryId = territory.TerritoryId;
        return Task.FromResult(territoryDto);
    }

    public Task<bool> UpdateTerritoryAsync(TerritoryDto territoryDto)
    {
        var territory = context.Territories.Find(territoryDto.TerritoryId);
        if (territory == null)
        {
            return Task.FromResult(false);  
        }

        territory.Title = territoryDto.Name;
        territory.ModifiedDate = DateTime.Now;

        context.SaveChanges();
        return Task.FromResult(true);
    }

    public Task<bool> DeleteTerritoryAsync(int id)
    {
        var territory = context.Territories.Find(id);
        if (territory == null)
        {
            return Task.FromResult(false);
        }

        context.Territories.Remove(territory);
        context.SaveChanges();
        return Task.FromResult(true);
    }
}