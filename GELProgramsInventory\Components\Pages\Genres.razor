@page "/genres"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Components.Shared
@inject GenreService GenreService
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Genres" />

<h1>Genres</h1>

<SfButton OnClick="@OpenCreateDialog">Create New Genre</SfButton>
<SfDialog @ref="dlgGenre" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="600px" Visible="false" Height="400px">
    <DialogTemplates>
        <Header>Genre Information</Header>
        <Content>
            <EditForm Model="_selectedGenre" OnValidSubmit="SaveGenre" FormName="GenreForm">
                <div class="row">
                    <div class="col-md mb-2">
                        <SfTextBox Placeholder="Genre Name" FloatLabelType="FloatLabelType.Always"
                                   @bind-Value="_selectedGenre.GenreName"></SfTextBox>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md" style="display: flex; align-items: center; gap: 5px;" >
                        <span><b>Status</b></span> <SfSwitch @bind-Checked="_selectedGenre.IsActive"></SfSwitch> <span> @_selectedGenre.Status</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md">
                        <SfButton CssClass="e-primary">Save</SfButton>
                        
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
<SfGrid @ref="_grid" DataSource="@_genresList" AllowPaging="true">
    <GridColumns>
        
        <GridColumn Field="@nameof(GenreDto.GenreName)" HeaderText="Genre Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(GenreDto.Status)" HeaderText="Status" AutoFit="true" ></GridColumn>
        
        <GridColumn HeaderText="Actions" Width="150">
            <Template Context="genreContext">
                <SfButton OnClick="@(() => OpenEditDialog((GenreDto)genreContext))">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteGenre(((GenreDto)genreContext).GenreId))">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private List<GenreDto> _genresList = new();
    private SfGrid<GenreDto> _grid = new();
    private GenreDto _selectedGenre = new();
    private SfDialog? dlgGenre;

    
    protected override async Task OnInitializedAsync()
    {
        _genresList = await GenreService.GetGenreDtosAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedGenre = new GenreDto() {IsActive = true};
        await dlgGenre!.ShowAsync();
    }

    private async Task OpenEditDialog(GenreDto genre)
    {
        _selectedGenre = genre;
        await dlgGenre!.ShowAsync();
    }

    private async Task SaveGenre()
    {
        if (_selectedGenre.GenreId == 0)
        {
            await GenreService.CreateGenreAsync(_selectedGenre);
        }
        else
        {
            await GenreService.UpdateGenreAsync(_selectedGenre);
        }

        _genresList = await GenreService.GetGenreDtosAsync();
        await _grid.Refresh();
        await dlgGenre!.HideAsync();
    }

    private async Task DeleteGenre(int genreId)
    {
        await GenreService.DeleteGenreAsync(genreId);
        _genresList = await GenreService.GetGenreDtosAsync();
        await _grid.Refresh();
    }

}
