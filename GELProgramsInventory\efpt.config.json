﻿{
   "CodeGenerationMode": 5,
   "ContextClassName": "ApplicationDbContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "GELProgramsInventory",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[Client]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ContractDocuments]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Currencies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Genre]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Languages]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Menus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[OnAirFormat]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ProductionHouse]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Programs]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RoleMenus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Roles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SyndicationFormat]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Syndications]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Territories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserRoles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Users]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": true,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}