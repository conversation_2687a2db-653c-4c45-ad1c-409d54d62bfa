using GELProgramsInventory.Models;
using GELProgramsInventory.Models.Dtos;

namespace GELProgramsInventory.Services;

public class ProductionHouseService(ApplicationDbContext context)
{
    public Task<List<ProductionHouseDto>> GetProductionHousesAsync()
    {
        var res = context.ProductionHouses
            .Select(ph => new ProductionHouseDto
            {
                ProductionHouseId = ph.ProductionHouseId,
                ProductionHouseName = ph.ProductionHouseName,
                ContactInfo = ph.ContactInfo,
                IsActive = ph.IsActive
            })
            .ToList();
        return Task.FromResult(res);
    }

    public Task<ProductionHouseDto?> GetProductionHouseByIdAsync(int id)
    {
        var productionHouse = context.ProductionHouses.Find(id);
        if (productionHouse == null) return Task.FromResult<ProductionHouseDto?>(null);

        var oo = new ProductionHouseDto
        {
            ProductionHouseId = productionHouse.ProductionHouseId,
            ProductionHouseName = productionHouse.ProductionHouseName,
            ContactInfo = productionHouse.ContactInfo,
            IsActive = productionHouse.IsActive
        };
        return Task.FromResult<ProductionHouseDto?>(oo);
    }

    public Task<ProductionHouse> CreateProductionHouseAsync(ProductionHouseDto productionHouseDto)
    {
        var productionHouse = new ProductionHouse
        {
            ProductionHouseName = productionHouseDto.ProductionHouseName,
            ContactInfo = productionHouseDto.ContactInfo,
            IsActive = productionHouseDto.IsActive,
            CreatedDate = DateTime.Now
        };

        context.ProductionHouses.Add(productionHouse);
        context.SaveChanges();
        return Task.FromResult(productionHouse);
    }

    public Task UpdateProductionHouseAsync(ProductionHouseDto productionHouseDto)
    {
        var productionHouse = context.ProductionHouses.Find(productionHouseDto.ProductionHouseId);
        if (productionHouse != null)
        {
            productionHouse.ProductionHouseName = productionHouseDto.ProductionHouseName;
            productionHouse.ContactInfo = productionHouseDto.ContactInfo;
            productionHouse.IsActive = productionHouseDto.IsActive;
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }

    public Task DeleteProductionHouseAsync(int id)
    {
        var productionHouse = context.ProductionHouses.Find(id);
        if (productionHouse != null)
        {
            context.ProductionHouses.Remove(productionHouse);
            context.SaveChanges();
        }

        return Task.CompletedTask;
    }
}