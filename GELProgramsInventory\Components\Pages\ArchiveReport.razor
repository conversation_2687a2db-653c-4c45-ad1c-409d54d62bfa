@page "/reports/archive"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@inject ProgramService ProgramService
@inject SyndicationService SyndicationService
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer
@layout Components.Layout.BlankLayout

<div class="archive-report-container">
    <div class="report-header">
        <div class="header-content">
            <h1>Archive in Post & Broadcast Operation Report</h1>
            <p class="report-subtitle no-print">Complete archive report for all programs</p>
        </div>
        <div class="header-actions no-print">
            <button class="print-btn" @onclick="PrintReport">
                Print Report
            </button>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="spinner"></div>
            <p>Loading report data...</p>
        </div>
    }
    else
    {
        <div class="report-content">
            <div class="table-container">
                <table class="archive-table">
                    <thead>
                        <tr>
                            <th>PROGRAMS NAME</th>
                            <th>GENRE</th>
                            <th>No of Eps</th>
                            <th>Duration (min)</th>
                            <th>ONAIR FORMAT</th>
                            <th>YEAR OF LAUNCH</th>
                            <th>DRIVE SERIAL NUMBER</th>
                            <th>SYNDICATION</th>
                            <th>SYNDICATION FORMAT</th>
                            <th>CLIENT NAME</th>
                            <th>PRODUCTION HOUSE</th>
                            <th>REMARK</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in reportData)
                        {
                            <tr>
                                <td class="program-name">@item.ProgramName</td>
                                <td>@item.Genre</td>
                                <td class="text-center">@item.NoOfEps</td>
                                <td class="text-center">@item.Duration</td>
                                <td>@item.OnAirFormat</td>
                                <td class="text-center">@item.YearOfLaunch</td>
                                <td>@item.DriveSerialNumber</td>
                                <td class="syndication-info">@item.Syndication</td>
                                <td>@item.SyndicationFormat</td>
                                <td>@item.ClientName</td>
                                <td>@item.ProductionHouse</td>
                                <td class="remark">@item.Remark</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
</div>

<style>
    .archive-report-container {
        padding: 10px;
        max-width: 100%;
        margin: 0;
    }

    .report-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .header-content h1 {
        color: #2c3e50;
        font-size: 22px;
        font-weight: 600;
        margin: 0;
    }

    .report-subtitle {
        color: #6c757d;
        font-size: 12px;
        margin: 3px 0 0 0;
    }

    .print-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }

    .print-btn:hover {
        background: #0056b3;
    }

    .loading-container {
        text-align: center;
        padding: 20px;
    }

    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .table-container {
        overflow-x: auto;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
    }

    .archive-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        font-size: 13px;
    }

    .archive-table thead {
        background: #343a40;
        color: white;
    }

    .archive-table th {
        padding: 8px 6px;
        text-align: left;
        font-weight: 600;
        font-size: 11px;
        border-right: 1px solid #495057;
    }

    .archive-table th:last-child {
        border-right: none;
    }

    .archive-table td {
        padding: 6px 4px;
        border-bottom: 1px solid #e9ecef;
        border-right: 1px solid #e9ecef;
        vertical-align: top;
        font-size: 12px;
    }

    .archive-table td:last-child {
        border-right: none;
    }

    .archive-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .archive-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .program-name {
        font-weight: 600;
        color: #2c3e50;
        min-width: 120px;
    }

    .text-center {
        text-align: center;
    }

    .syndication-info {
        max-width: 200px;
        word-wrap: break-word;
        font-size: 12px;
    }

    .remark {
        max-width: 150px;
        word-wrap: break-word;
        font-size: 12px;
    }

    @@media print {
        .no-print {
            display: none !important;
        }

        .archive-report-container {
            padding: 0;
        }

        .report-header {
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
        }

        .header-content h1 {
            font-size: 24px;
            color: #000;
        }

        .archive-table {
            font-size: 10px;
        }

        .archive-table th {
            padding: 6px 4px;
            background: #f0f0f0 !important;
            color: #000 !important;
            border: 1px solid #000;
        }

        .archive-table td {
            padding: 4px;
            border: 1px solid #000;
        }

        .table-container {
            box-shadow: none;
        }

        @@page {
            margin: 0.5in;
            size: landscape;
        }
    }

    @@media screen and (max-width: 768px) {
        .report-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .archive-table {
            font-size: 11px;
        }

        .archive-table th,
        .archive-table td {
            padding: 6px 4px;
        }
    }
</style>

@code {
    private List<ArchiveReportDto> reportData = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadReportData();
    }

    private async Task LoadReportData()
    {
        isLoading = true;
        try
        {
            var programs = await ProgramService.GetProgramsAsync();
            var syndications = await SyndicationService.GetSyndicationsAsync();

            reportData = programs.Select(p => new ArchiveReportDto
            {
                ProgramName = p.ProgramName,
                Genre = p.GenreName ?? "",
                NoOfEps = p.TotalEpisodes??0,
                Duration = p.Duration ?? 0,
                OnAirFormat = p.OnAirFormatName ?? "",
                YearOfLaunch = p.YearOfLaunch ?? "",
                DriveSerialNumber = p.DriveSerialNumber ?? "",
                Syndication = GetSyndicationSummary(p.ProgramId, syndications),
                SyndicationFormat = GetSyndicationFormats(p.ProgramId, syndications),
                ClientName = GetClientNames(p.ProgramId, syndications),
                ProductionHouse = p.ProductionHouseName ?? "",
                Remark = p.Remarks ?? ""
            }).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading report data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetSyndicationSummary(int programId, List<SyndicationDto> syndications)
    {
        var programSyndications = syndications.Where(s => s.ProgramId == programId).ToList();
        if (!programSyndications.Any()) return "";

        var summaries = programSyndications.Select(s => 
            $"send {s.EpisodeTo - s.EpisodeFrom + 1} eps to {s.TerritoryName}").ToList();
        
        return string.Join(", ", summaries);
    }

    private string GetSyndicationFormats(int programId, List<SyndicationDto> syndications)
    {
        var formats = syndications.Where(s => s.ProgramId == programId)
            .Select(s => s.SyndicationFormatName)
            .Where(f => !string.IsNullOrEmpty(f))
            .Distinct()
            .ToList();
        
        return string.Join(", ", formats);
    }

    private string GetClientNames(int programId, List<SyndicationDto> syndications)
    {
        var clients = syndications.Where(s => s.ProgramId == programId)
            .Select(s => s.ClientName)
            .Where(c => !string.IsNullOrEmpty(c))
            .Distinct()
            .ToList();
        
        return string.Join(", ", clients);
    }

    private async Task PrintReport()
    {
        await JSRuntime.InvokeVoidAsync("window.print");
    }

    public class ArchiveReportDto
    {
        public string ProgramName { get; set; } = "";
        public string Genre { get; set; } = "";
        public int NoOfEps { get; set; }
        public int Duration { get; set; }
        public string OnAirFormat { get; set; } = "";
        public string YearOfLaunch { get; set; } = "";
        public string DriveSerialNumber { get; set; } = "";
        public string Syndication { get; set; } = "";
        public string SyndicationFormat { get; set; } = "";
        public string ClientName { get; set; } = "";
        public string ProductionHouse { get; set; } = "";
        public string Remark { get; set; } = "";
    }
}