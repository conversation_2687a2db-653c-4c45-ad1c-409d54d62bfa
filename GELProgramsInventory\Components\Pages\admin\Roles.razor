@page "/admin/roles"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Components.Shared
@inject GELProgramsInventory.Services.RoleService RoleService
@inject GELProgramsInventory.Services.MenuService MenuService
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Admin" SubPage="Roles" />

<div style="display: flex; justify-content: space-between; align-items: flex-end;" class="mb-2 me-2">
    <div class="">
        <h1 class="mb-0">Roles Management</h1>
        <p class="page-description">Manage user roles and their menu permissions</p>
    </div>

    <div class="">
        <SfButton OnClick="OpenCreateDialog" CssClass="e-primary e-large" aria-label="Add new role">
            <span class="e-btn-icon e-icons e-plus"></span>
            Add New Role
        </SfButton>
    </div>
</div>

<SfGrid @ref="_grid" DataSource="@_rolesList" AllowPaging="true" AllowSorting="true">
    <GridColumns>
        <GridColumn Field="@nameof(RoleDto.RoleTitle)" HeaderText="Role Title" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(RoleDto.Status)" HeaderText="Status" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(RoleDto.CreatedDate)" HeaderText="Created Date" Format="dd/MM/yyyy" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(RoleDto.ModifiedDate)" HeaderText="Modified Date" Format="dd/MM/yyyy" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Assigned Menus" AutoFit="true">
            <Template Context="roleContext">
                @{
                    var role = (RoleDto)roleContext;
                    var menuTitles = role.AssignedMenus.Select(m => m.MemuTitle).ToList();
                }
                @string.Join(", ", menuTitles)
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Actions" Width="200">
            <Template Context="roleContext">
                <SfButton OnClick="@(() => OpenEditDialog((RoleDto)roleContext))" CssClass="e-info">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteRole(((RoleDto)roleContext).RoleId))" CssClass="e-danger">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dlgRole" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="800px" Visible="false">
    <DialogTemplates>
        <Header>Role Information</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="_selectedRole" OnValidSubmit="SaveRole" FormName="RoleForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>Role Title</label>
                                <SfTextBox Placeholder="Role Title" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedRole.RoleTitle"></SfTextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>Status</label>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <SfSwitch @bind-Checked="_selectedRole.RoleIsActive"></SfSwitch> 
                                    <span>@_selectedRole.Status</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label>Assigned Menus</label>
                        <SfMultiSelect TValue="int[]" TItem="MenuDto" 
                                      DataSource="@_menusList" 
                                      @bind-Value="_selectedMenuIds"
                                      Placeholder="Select menus to assign"
                                      Mode="VisualMode.CheckBox">
                            <MultiSelectFieldSettings Text="MemuTitle" Value="MenuId"></MultiSelectFieldSettings>
                            <MultiSelectTemplates TItem="MenuDto">
                                <ItemTemplate Context="menuItem">
                                    <div>
                                        @if (!string.IsNullOrEmpty(menuItem.Module))
                                        {
                                            <span class="badge badge-secondary me-1">@menuItem.Module</span>
                                        }
                                        @menuItem.MemuTitle
                                    </div>
                                </ItemTemplate>
                            </MultiSelectTemplates>
                        </SfMultiSelect>
                    </div>

                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<RoleDto> _rolesList = new();
    private List<MenuDto> _menusList = new();
    private SfGrid<RoleDto> _grid = new();
    private RoleDto _selectedRole = new();
    private SfDialog? dlgRole;
    private int[] _selectedMenuIds = Array.Empty<int>();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _rolesList = await RoleService.GetRolesAsync();
        _menusList = await MenuService.GetMenusAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedRole = new RoleDto() { RoleIsActive = true, CreatedBy = "system", ModifiedBy = "system" };
        _selectedMenuIds = Array.Empty<int>();
        if (dlgRole != null)
        {
            await dlgRole.ShowAsync();
        }
    }

    private async Task OpenEditDialog(RoleDto role)
    {
        _selectedRole = role;
        _selectedMenuIds = role.AssignedMenuIds.ToArray();
        if (dlgRole != null)
        {
            await dlgRole.ShowAsync();
        }
    }

    private async Task SaveRole()
    {
        _selectedRole.AssignedMenuIds = _selectedMenuIds.ToList();
        
        if (_selectedRole.RoleId == 0)
        {
            await RoleService.CreateRoleAsync(_selectedRole);
        }
        else
        {
            await RoleService.UpdateRoleAsync(_selectedRole);
        }

        await LoadData();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
        await CloseDialog();
    }

    private async Task DeleteRole(int roleId)
    {
        await RoleService.DeleteRoleAsync(roleId);
        await LoadData();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
    }

    private async Task CloseDialog()
    {
        if (dlgRole != null)
        {
            await dlgRole.HideAsync();
        }
    }
}
