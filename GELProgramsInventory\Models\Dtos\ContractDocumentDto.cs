using System.ComponentModel.DataAnnotations;

namespace GELProgramsInventory.Models.Dtos;

public class ContractDocumentDto
{
    public Guid Id { get; set; }

    [Required(ErrorMessage = "File name is required")]
    [StringLength(1000, ErrorMessage = "File name cannot exceed 1000 characters")]
    public string FileName { get; set; } = string.Empty;

    public byte[]? FileContent { get; set; }

    [Required(ErrorMessage = "Syndication is required")]
    public int SyndicationId { get; set; }

    public DateTime UploadedDate { get; set; }

    [StringLength(300, ErrorMessage = "Uploaded by cannot exceed 300 characters")]
    public string? UploadedBy { get; set; }

    public bool IsActive { get; set; } = true;

    // Additional properties for UI
    public string? SyndicationName { get; set; }
    public long FileSizeBytes { get; set; }
    public string FileSizeFormatted => FormatFileSize(FileSizeBytes);
    public string FileExtension => Path.GetExtension(FileName)?.ToLowerInvariant() ?? string.Empty;
    public bool IsPdf => FileExtension == ".pdf";

    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }
}
