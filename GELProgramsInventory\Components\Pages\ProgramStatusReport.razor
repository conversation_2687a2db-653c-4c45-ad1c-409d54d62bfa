@page "/reports/program-status"
@page "/reports/program-status/{ProgramId:int}"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using Syncfusion.Blazor.Grids
@inject ProgramService ProgramService
@inject SyndicationService SyndicationService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer
@layout Components.Layout.BlankLayout

<div class="program-status-container">
    @if (ProgramId == null)
    {
        <!-- Program Selection View -->
        <div class="report-header">
            <div class="header-content">
                <h1>Program Status Report</h1>
                <p class="report-subtitle no-print">Select a program to view detailed status report</p>
            </div>
        </div>

        @if (isLoading)
        {
            <div class="loading-container">
                <div class="spinner"></div>
                <p>Loading programs...</p>
            </div>
        }
        else
        {
            <div class="programs-grid-container">
                <SfGrid DataSource="@programs" AllowPaging="true" PageSize="20" AllowSorting="true" 
                        AllowFiltering="true" FilterSettings="@FilterSettings" Height="600">
                    <GridPageSettings PageSize="20"></GridPageSettings>
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="@nameof(ProgramDto.ProgramName)" HeaderText="Program Name" Width="200">
                            <Template>
                                @{
                                    var program = (context as ProgramDto);
                                }
                                <div class="program-link" @onclick="() => ViewProgramReport(program.ProgramId)">
                                    <strong>@program.ProgramName</strong>
                                </div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="@nameof(ProgramDto.GenreName)" HeaderText="Genre" Width="120"></GridColumn>
                        <GridColumn Field="@nameof(ProgramDto.TotalEpisodes)" HeaderText="Episodes" Width="100" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="@nameof(ProgramDto.Duration)" HeaderText="Duration (min)" Width="120" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="@nameof(ProgramDto.OnAirFormatName)" HeaderText="On-Air Format" Width="130"></GridColumn>
                        <GridColumn Field="@nameof(ProgramDto.YearOfLaunch)" HeaderText="Year" Width="80" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="@nameof(ProgramDto.ProductionHouseName)" HeaderText="Production House" Width="180"></GridColumn>
                        <GridColumn HeaderText="Action" Width="100" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var program = (context as ProgramDto);
                                }
                                <button class="view-btn" @onclick="() => ViewProgramReport(program.ProgramId)">
                                    View Report
                                </button>
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        }
    }
    else
    {
        <!-- Program Report View -->
        <div class="report-header">
            <div class="header-content">
                <h1>Program Status Report</h1>
                <p class="report-subtitle no-print">Detailed status for @selectedProgram?.ProgramName</p>
            </div>
            <div class="header-actions no-print">
                <button class="back-btn" @onclick="BackToList">
                    Back to List
                </button>
                <button class="print-btn" @onclick="PrintReport">
                    Print Report
                </button>
            </div>
        </div>

        @if (selectedProgram != null)
        {
            <div class="report-content">
                <!-- Program Header Information -->
                <div class="program-info-section">
                    <h2>Program Information</h2>
                    <div class="program-details-grid">
                        <div class="detail-row">
                            <span class="label">PROGRAMS NAME:</span>
                            <span class="value">@selectedProgram.ProgramName</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">GENRE:</span>
                            <span class="value">@selectedProgram.GenreName</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">No of Eps:</span>
                            <span class="value">@selectedProgram.TotalEpisodes</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Duration (min):</span>
                            <span class="value">@selectedProgram.Duration</span>
                        </div>
                    </div>
                </div>

                <!-- Syndication Details -->
                <div class="syndication-section">
                    <h2>Syndication Details</h2>
                    
                    @if (syndicationDetails.Any())
                    {
                        <div class="syndication-table-container">
                            <table class="syndication-table">
                                <thead>
                                    <tr>
                                        <th>Country Name</th>
                                        <th>Client</th>
                                        <th>Language</th>
                                        <th>Total Episodes</th>
                                        <th>Episode Range</th>
                                        <th>Rate/Episode</th>
                                        <th>Total Amount</th>
                                        <th>Currency</th>
                                        <th>Contract Period</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var syndication in syndicationDetails)
                                    {
                                        <tr>
                                            <td class="country">@syndication.TerritoryName</td>
                                            <td class="client">@syndication.ClientName</td>
                                            <td class="language">@syndication.LanguageName</td>
                                            <td class="episodes-count">@syndication.TotalEpisodes</td>
                                            <td class="episode-range">@syndication.EpisodeFrom - @syndication.EpisodeTo</td>
                                            <td class="rate">@syndication.PricePerEpisode?.ToString("N2")</td>
                                            <td class="amount">@syndication.TotalAmount?.ToString("N2")</td>
                                            <td class="currency">@syndication.CurrencyCode</td>
                                            <td class="contract-period">
                                                @if (syndication.ContractStartDate.HasValue && syndication.ContractEndDate.HasValue)
                                                {
                                                    <div class="contract-dates">
                                                        <div>@syndication.ContractStartDate?.ToString("dd-MMM-yyyy")</div>
                                                        <div>@syndication.ContractEndDate?.ToString("dd-MMM-yyyy")</div>
                                                    </div>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="no-syndication">
                            <p>No syndication details available for this program.</p>
                        </div>
                    }
                </div>
            </div>
        }
    }
</div>

<style>
    .program-status-container {
        padding: 10px;
        max-width: 100%;
        margin: 0;
    }

    .report-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .header-content h1 {
        color: #2c3e50;
        font-size: 22px;
        font-weight: 600;
        margin: 0;
    }

    .report-subtitle {
        color: #6c757d;
        font-size: 12px;
        margin: 3px 0 0 0;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .back-btn, .print-btn, .view-btn {
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
    }

    .back-btn {
        background: #6c757d;
        color: white;
    }

    .back-btn:hover {
        background: #545b62;
    }

    .print-btn {
        background: #007bff;
        color: white;
    }

    .print-btn:hover {
        background: #0056b3;
    }

    .view-btn {
        background: #28a745;
        color: white;
        padding: 5px 10px;
        font-size: 12px;
    }

    .view-btn:hover {
        background: #1e7e34;
    }

    .loading-container {
        text-align: center;
        padding: 20px;
    }

    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .programs-grid-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .program-link {
        cursor: pointer;
        color: #007bff;
        padding: 5px;
        border-radius: 3px;
        transition: background-color 0.3s;
    }

    .program-link:hover {
        background-color: #e3f2fd;
        text-decoration: underline;
    }

    .report-content {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .program-info-section {
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .program-info-section h2 {
        color: #2c3e50;
        font-size: 18px;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .program-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 10px;
    }

    .detail-row {
        display: flex;
        padding: 8px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .detail-row .label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
        margin-right: 15px;
    }

    .detail-row .value {
        color: #2c3e50;
        font-weight: 500;
    }

    .syndication-section {
        padding: 15px;
    }

    .syndication-section h2 {
        color: #2c3e50;
        font-size: 18px;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .syndication-table-container {
        overflow-x: auto;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .syndication-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        font-size: 14px;
        min-width: 1000px;
    }

    .syndication-table thead {
        background: #343a40;
        color: white;
    }

    .syndication-table th {
        padding: 8px 6px;
        text-align: left;
        font-weight: 600;
        font-size: 11px;
        border-right: 1px solid #495057;
        white-space: nowrap;
    }

    .syndication-table th:last-child {
        border-right: none;
    }

    .syndication-table td {
        padding: 6px 4px;
        border-bottom: 1px solid #e9ecef;
        border-right: 1px solid #e9ecef;
        vertical-align: top;
        font-size: 12px;
    }

    .syndication-table td:last-child {
        border-right: none;
    }

    .syndication-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .syndication-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .country, .client {
        font-weight: 600;
        color: #2c3e50;
    }

    .episodes-count, .rate, .amount {
        text-align: right;
        font-family: 'Courier New', monospace;
    }

    .episode-range {
        text-align: center;
        font-family: 'Courier New', monospace;
    }

    .currency {
        text-align: center;
        font-weight: 600;
        color: #28a745;
    }

    .contract-dates {
        text-align: center;
        font-size: 12px;
    }

    .contract-dates div:first-child {
        font-weight: 600;
        color: #28a745;
    }

    .contract-dates div:last-child {
        color: #dc3545;
        margin-top: 2px;
    }

    .no-syndication {
        text-align: center;
        padding: 40px;
        color: #6c757d;
        background: #f8f9fa;
        border-radius: 8px;
    }

    @@media print {
        .no-print {
            display: none !important;
        }

        .program-status-container {
            padding: 0;
        }

        .report-header {
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
        }

        .header-content h1 {
            font-size: 24px;
            color: #000;
        }

        .program-info-section, .syndication-section {
            padding: 20px 0;
        }

        .syndication-table {
            font-size: 11px;
        }

        .syndication-table th {
            padding: 8px 6px;
            background: #f0f0f0 !important;
            color: #000 !important;
            border: 1px solid #000;
        }

        .syndication-table td {
            padding: 6px;
            border: 1px solid #000;
        }

        .syndication-table-container {
            box-shadow: none;
        }

        @@page {
            margin: 0.5in;
            size: landscape;
        }
    }

    @@media screen and (max-width: 768px) {
        .report-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .program-details-grid {
            grid-template-columns: 1fr;
        }

        .syndication-table {
            font-size: 12px;
        }

        .syndication-table th,
        .syndication-table td {
            padding: 8px 6px;
        }
    }
</style>

@code {
    [Parameter] public int? ProgramId { get; set; }
    
    private List<ProgramDto> programs = new();
    private ProgramDto? selectedProgram;
    private List<SyndicationDetailDto> syndicationDetails = new();
    private bool isLoading = true;

    private GridFilterSettings FilterSettings = new GridFilterSettings
    {
        Type = Syncfusion.Blazor.Grids.FilterType.Excel
    };

    protected override async Task OnInitializedAsync()
    {
        if (ProgramId.HasValue)
        {
            await LoadProgramReport(ProgramId.Value);
        }
        else
        {
            await LoadPrograms();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ProgramId.HasValue)
        {
            await LoadProgramReport(ProgramId.Value);
        }
        else
        {
            await LoadPrograms();
        }
    }

    private async Task LoadPrograms()
    {
        isLoading = true;
        try
        {
            programs = await ProgramService.GetProgramsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading programs: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadProgramReport(int programId)
    {
        isLoading = true;
        try
        {
            selectedProgram = await ProgramService.GetProgramByIdAsync(programId);
            if (selectedProgram != null)
            {
                var allSyndications = await SyndicationService.GetSyndicationsAsync();
                syndicationDetails = allSyndications
                    .Where(s => s.ProgramId == programId)
                    .Select(s => new SyndicationDetailDto
                    {
                        TerritoryName = s.TerritoryName ?? "",
                        ClientName = s.ClientName ?? "",
                        LanguageName = s.LanguageName ?? "",
                        TotalEpisodes = (s.EpisodeTo ?? 0) - (s.EpisodeFrom ?? 0) + 1,
                        EpisodeFrom = s.EpisodeFrom ?? 0,
                        EpisodeTo = s.EpisodeTo ?? 0,
                        PricePerEpisode = s.PricePerEpisode,
                        TotalAmount = s.PricePerEpisode * ((s.EpisodeTo ?? 0) - (s.EpisodeFrom ?? 0) + 1),
                        CurrencyCode = s.CurrencyCode ?? "",
                        ContractStartDate = s.ContractStartDate,
                        ContractEndDate = s.ContractEndDate
                    }).ToList();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading program report: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ViewProgramReport(int programId)
    {
        Navigation.NavigateTo($"/reports/program-status/{programId}");
    }

    private void BackToList()
    {
        Navigation.NavigateTo("/reports/program-status");
    }

    private async Task PrintReport()
    {
        await JSRuntime.InvokeVoidAsync("window.print");
    }

    public class SyndicationDetailDto
    {
        public string TerritoryName { get; set; } = "";
        public string ClientName { get; set; } = "";
        public string LanguageName { get; set; } = "";
        public int TotalEpisodes { get; set; }
        public int EpisodeFrom { get; set; }
        public int EpisodeTo { get; set; }
        public decimal? PricePerEpisode { get; set; }
        public decimal? TotalAmount { get; set; }
        public string CurrencyCode { get; set; } = "";
        public DateTime? ContractStartDate { get; set; }
        public DateTime? ContractEndDate { get; set; }
    }
}