﻿<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>
            <FluentNavLink Href="/programs" Icon="@(new Icons.Regular.Size20.Tv())" IconColor="Color.Accent">Programs</FluentNavLink>
            <FluentNavLink Href="/syndications" Icon="@(new Icons.Regular.Size20.Status())" IconColor="Color.Accent">Syndications</FluentNavLink>
            <FluentNavLink Href="/clients" Icon="@(new Icons.Regular.Size20.People())" IconColor="Color.Accent">Clients</FluentNavLink>
            <FluentNavLink Href="/production-houses" Icon="@(new Icons.Regular.Size20.Building())" IconColor="Color.Accent">Production Houses</FluentNavLink>
            <FluentNavGroup Title="Reports"  Icon="@(new Icons.Regular.Size20.DocumentTable())" IconColor="Color.Accent" >
                <FluentNavLink Href="/reports/archive" Icon="@(new Icons.Regular.Size20.Archive())" IconColor="Color.Accent" Target="ar">Archive Report</FluentNavLink>
                <FluentNavLink Href="/reports/program-status" Icon="@(new Icons.Regular.Size20.DocumentBulletList())" IconColor="Color.Accent" Target="sr">Program Status</FluentNavLink>
            </FluentNavGroup>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
}
