@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@inject GELProgramsInventory.Services.MenuService MenuService
@inject GELProgramsInventory.Services.UserService UserService
@rendermode InteractiveServer

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>

            <!-- Static Core Menu Items -->
            <FluentNavLink Href="/programs" Icon="@(new Icons.Regular.Size20.Tv())" IconColor="Color.Accent">Programs</FluentNavLink>
            <FluentNavLink Href="/syndications" Icon="@(new Icons.Regular.Size20.Status())" IconColor="Color.Accent">Syndications</FluentNavLink>
            <FluentNavLink Href="/clients" Icon="@(new Icons.Regular.Size20.People())" IconColor="Color.Accent">Clients</FluentNavLink>
            <FluentNavLink Href="/production-houses" Icon="@(new Icons.Regular.Size20.Building())" IconColor="Color.Accent">Production Houses</FluentNavLink>

            <!-- Dynamic Menu Items without Module (Root Level) -->
            @if (_rootMenus.Any())
            {
                @foreach (var menu in _rootMenus)
                {
                    <FluentNavLink Href="@menu.MenuLink" Icon="@(new Icons.Regular.Size20.Circle())" IconColor="Color.Accent">@menu.MemuTitle</FluentNavLink>
                }
            }

            <!-- Reports Module -->
            @if (_reportsMenus.Any() || _showStaticReports)
            {
                <FluentNavGroup Title="Reports" Icon="@(new Icons.Regular.Size20.DocumentTable())" IconColor="Color.Accent">
                    @if (_showStaticReports)
                    {
                        <FluentNavLink Href="/reports/archive" Icon="@(new Icons.Regular.Size20.Archive())" IconColor="Color.Accent" Target="ar">Archive Report</FluentNavLink>
                        <FluentNavLink Href="/reports/program-status" Icon="@(new Icons.Regular.Size20.DocumentBulletList())" IconColor="Color.Accent" Target="sr">Program Status</FluentNavLink>
                    }
                    @foreach (var menu in _reportsMenus)
                    {
                        <FluentNavLink Href="@menu.MenuLink" Icon="@(new Icons.Regular.Size20.Document())" IconColor="Color.Accent">@menu.MemuTitle</FluentNavLink>
                    }
                </FluentNavGroup>
            }

            <!-- Admin Module (Only for Admin Users) -->
            @if (_isAdminUser)
            {
                <FluentNavGroup Title="Admin" Icon="@(new Icons.Regular.Size20.Settings())" IconColor="Color.Accent">
                    <FluentNavLink Href="/admin/users" Icon="@(new Icons.Regular.Size20.Person())" IconColor="Color.Accent">Users</FluentNavLink>
                    <FluentNavLink Href="/admin/roles" Icon="@(new Icons.Regular.Size20.PersonAccounts())" IconColor="Color.Accent">Roles</FluentNavLink>
                    <FluentNavLink Href="/admin/menus" Icon="@(new Icons.Regular.Size20.Navigation())" IconColor="Color.Accent">Menus</FluentNavLink>
                    @foreach (var menu in _adminMenus)
                    {
                        <FluentNavLink Href="@menu.MenuLink" Icon="@(new Icons.Regular.Size20.Settings())" IconColor="Color.Accent">@menu.MemuTitle</FluentNavLink>
                    }
                </FluentNavGroup>
            }

            <!-- Other Dynamic Modules -->
            @foreach (var moduleGroup in _otherModuleMenus)
            {
                <FluentNavGroup Title="@moduleGroup.Key" Icon="@(new Icons.Regular.Size20.Folder())" IconColor="Color.Accent">
                    @foreach (var menu in moduleGroup.Value)
                    {
                        <FluentNavLink Href="@menu.MenuLink" Icon="@(new Icons.Regular.Size20.Circle())" IconColor="Color.Accent">@menu.MemuTitle</FluentNavLink>
                    }
                </FluentNavGroup>
            }
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
    private bool _isAdminUser = false;
    private bool _showStaticReports = true;
    private List<MenuDto> _rootMenus = new();
    private List<MenuDto> _reportsMenus = new();
    private List<MenuDto> _adminMenus = new();
    private Dictionary<string, List<MenuDto>> _otherModuleMenus = new();
    private string _currentUserId = "khi-soft-056\\jawaid"; // This should come from authentication

    protected override async Task OnInitializedAsync()
    {
        await LoadMenus();
    }

    private async Task LoadMenus()
    {
        try
        {
            // Check if user is admin
            _isAdminUser = await UserService.IsUserAdminAsync(_currentUserId);

            // Get user's accessible menus
            var userMenus = await UserService.GetUserMenusAsync(_currentUserId);

            // Group menus by module
            _rootMenus = userMenus.Where(m => string.IsNullOrEmpty(m.Module)).ToList();
            _reportsMenus = userMenus.Where(m => m.Module == "Reports").ToList();
            _adminMenus = userMenus.Where(m => m.Module == "Admin").ToList();

            // Group other modules
            _otherModuleMenus = userMenus
                .Where(m => !string.IsNullOrEmpty(m.Module) && m.Module != "Reports" && m.Module != "Admin")
                .GroupBy(m => m.Module!)
                .ToDictionary(g => g.Key, g => g.ToList());
        }
        catch (Exception)
        {
            // If there's an error loading dynamic menus, show static menus
            _showStaticReports = true;
        }
    }


}
