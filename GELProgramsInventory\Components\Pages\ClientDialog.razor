@using GELProgramsInventory.Models.Dtos
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons

<EditForm Model="@Client" OnValidSubmit="OnSave">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label>Client Name:</label>
        <SfTextBox @bind-Value="Client.ClientName"></SfTextBox>
    </div>

    <div class="form-group">
        <label>Contact Info:</label>
        <SfTextBox @bind-Value="Client.ContactInfo"></SfTextBox>
    </div>

    <div class="form-group">
        <SfCheckBox @bind-Checked="Client.IsActive" Label="Active"></SfCheckBox>
    </div>

    <SfButton IsPrimary="true" ButtonType="ButtonType.Submit">Save</SfButton>
</EditForm>

@code {
    [Parameter]
    public ClientDto Client { get; set; } = new();

    [Parameter]
    public EventCallback OnSave { get; set; }
}