@page "/production-houses"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Components.Shared
@inject ProductionHouseService ProductionHouseService
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Production Houses" />

<div style="display: flex; justify-content: space-between; align-items: flex-end;" class="mb-2 me-2">
    <div class="">
        <h1 class="mb-0">Production Houses</h1>
    </div>

    <div class="">
        <SfButton OnClick="OpenCreateDialog" CssClass="e-primary e-large" aria-label="Add new program">
            <span class="e-btn-icon e-icons e-plus"></span>
            Add New Production House
        </SfButton>
    </div>
</div>

<SfGrid @ref="_grid" DataSource="@_productionHousesList" AllowPaging="true">
    <GridColumns>
        <GridColumn Field="@nameof(ProductionHouseDto.ProductionHouseName)" HeaderText="Production House Name" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProductionHouseDto.ContactInfo)" HeaderText="Contact Info" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(ProductionHouseDto.Status)" HeaderText="Status" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Actions" Width="150">
            <Template Context="productionHouseContext">
                <SfButton OnClick="@(() => OpenEditDialog((ProductionHouseDto)productionHouseContext))" CssClass="e-info">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteProductionHouse(((ProductionHouseDto)productionHouseContext).ProductionHouseId))" CssClass="e-danger">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>


<SfDialog @ref="dlgProductionHouse" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="600px" Visible="false">
    <DialogTemplates>
        <Header>Production House Information</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="_selectedProductionHouse" OnValidSubmit="SaveProductionHouse" FormName="ProductionHouseForm">
                    <div class="form-group mb-3">
                        <label>Production House Name</label>
                        <SfTextBox Placeholder="Production House Name" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedProductionHouse.ProductionHouseName"></SfTextBox>
                    </div>
                    <div class="form-group mb-3">
                        <label>Contact Info</label>
                        <SfTextBox Placeholder="Contact Info" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedProductionHouse.ContactInfo"></SfTextBox>
                    </div>
                    <div class="form-group mb-3">
                        <label>Status</label>
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <SfSwitch @bind-Checked="_selectedProductionHouse.IsActive"></SfSwitch> <span> @_selectedProductionHouse.Status</span>
                        </div>
                    </div>
                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<style>
    .card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        margin-top: 1rem;
    }

    .card-header {
        background-color: #f5f5f5;
        padding: 1rem 1.5rem;
        font-size: 1.25rem;
        font-weight: 500;
        border-bottom: 1px solid #e0e0e0;
    }

    .card-body {
        padding: 1.5rem;
    }

    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e0e0e0;
    }
</style>

@code {
    private List<ProductionHouseDto> _productionHousesList = new();
    private SfGrid<ProductionHouseDto> _grid = new();
    private ProductionHouseDto _selectedProductionHouse = new();
    private SfDialog? dlgProductionHouse;

    protected override async Task OnInitializedAsync()
    {
        _productionHousesList = await ProductionHouseService.GetProductionHousesAsync();
    }

    private async Task OpenCreateDialog()
    {
        _selectedProductionHouse = new ProductionHouseDto() { IsActive = true };
        if (dlgProductionHouse != null)
        {
            await dlgProductionHouse.ShowAsync();
        }
    }

    private async Task OpenEditDialog(ProductionHouseDto productionHouse)
    {
        _selectedProductionHouse = productionHouse;
        if (dlgProductionHouse != null)
        {
            await dlgProductionHouse.ShowAsync();
        }
    }

    private async Task SaveProductionHouse()
    {
        if (_selectedProductionHouse.ProductionHouseId == 0)
        {
            await ProductionHouseService.CreateProductionHouseAsync(_selectedProductionHouse);
        }
        else
        {
            await ProductionHouseService.UpdateProductionHouseAsync(_selectedProductionHouse);
        }

        _productionHousesList = await ProductionHouseService.GetProductionHousesAsync();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
        await CloseDialog();
    }

    private async Task DeleteProductionHouse(int productionHouseId)
    {
        await ProductionHouseService.DeleteProductionHouseAsync(productionHouseId);
        _productionHousesList = await ProductionHouseService.GetProductionHousesAsync();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
    }

    private async Task CloseDialog()
    {
        if (dlgProductionHouse != null)
        {
            await dlgProductionHouse.HideAsync();
        }
    }
}
