﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <None Include="efpt.config.json.user" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="microsoft.entityframeworkcore" Version="9.0.7" />
    <PackageReference Include="microsoft.entityframeworkcore.design" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="microsoft.entityframeworkcore.sqlserver" Version="9.0.7" />
    <PackageReference Include="microsoft.entityframeworkcore.tools" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.11.5" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.11.5" />
    <PackageReference Include="syncfusion.blazor" Version="30.1.39" />
    <PackageReference Include="syncfusion.blazor.themes" Version="30.1.39" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="NewFolder\" />
    <Folder Include="wwwroot\bootstrap\" />
  </ItemGroup>
</Project>
