@page "/programs/add"
@page "/programs/edit/{ProgramId:int}"
@using GELProgramsInventory.Components.Shared
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@inject ProgramService ProgramService
@inject GenreService GenreService
@inject ProductionHouseService ProductionHouseService
@inject OnAirFormatService OnAirFormatService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Programs" SubPage="@(IsEditMode ? "Edit Program" : "Add Program")"/>

<div class="">
    <div class="card">
        <div class="card-header">
            @(IsEditMode ? "Edit Program" : "Add New Program")
        </div>
        <div class="card-body">
            <EditForm Model="CurrentProgram" OnValidSubmit="SaveProgram" FormName="ProgramForm">
                <DataAnnotationsValidator/>
                <ValidationSummary class="validation-summary"/>

                <div class="form-section">
                    <h5 class="section-title">Program Names</h5>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="required">Primary Name</label>
                            <SfTextBox @bind-value="CurrentProgram.PrimaryName" Placeholder="Enter primary program name" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                            <ValidationMessage For="@(() => CurrentProgram.PrimaryName)"/>
                        </div>
                        <div class="form-group">
                            <label>Secondary Name 1</label>
                            <SfTextBox @bind-value="CurrentProgram.SecondaryName1" Placeholder="Enter secondary name (optional)" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        </div>
                        <div class="form-group">
                            <label>Secondary Name 2</label>
                            <SfTextBox @bind-value="CurrentProgram.SecondaryName2" Placeholder="Enter secondary name (optional)" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        </div>
                        <div class="form-group">
                            <label>Secondary Name 3</label>
                            <SfTextBox @bind-value="CurrentProgram.SecondaryName3" Placeholder="Enter secondary name (optional)" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        </div>
                        <div class="form-group">
                            <label>Secondary Name 4</label>
                            <SfTextBox @bind-value="CurrentProgram.SecondaryName4" Placeholder="Enter secondary name (optional)" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="section-title">Program Details</h5>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Genre</label>
                            <div class="dropdown-with-add">
                                <SfComboBox TItem="GenreDto" TValue="int?" @bind-value="CurrentProgram.GenreId" DataSource="@Genres" Placeholder="Select a genre" FloatLabelType="FloatLabelType.Never">
                                    <ComboBoxFieldSettings Text="GenreName" Value="GenreId"></ComboBoxFieldSettings>
                                </SfComboBox>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddGenreDialog">Add New</SfButton>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Production House</label>
                            <div class="dropdown-with-add">
                                <SfComboBox TItem="ProductionHouseDto" TValue="int?" @bind-value="CurrentProgram.ProductionHouseId" DataSource="@ProductionHouses" Placeholder="Select a production house" FloatLabelType="FloatLabelType.Never">
                                    <ComboBoxFieldSettings Text="ProductionHouseName" Value="ProductionHouseId"></ComboBoxFieldSettings>
                                </SfComboBox>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddProductionHouseDialog">Add New</SfButton>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>On Air Format</label>
                            <div class="dropdown-with-add">
                                <SfComboBox TItem="OnAirFormatDto" TValue="int?" @bind-value="CurrentProgram.OnAirFormatId" DataSource="@OnAirFormats" Placeholder="Select an on air format" FloatLabelType="FloatLabelType.Never">
                                    <ComboBoxFieldSettings Text="@nameof(OnAirFormatDto.OnAirFormatName)" Value="@nameof(OnAirFormatDto.OnAirFormatId)"></ComboBoxFieldSettings>
                                </SfComboBox>
                                <SfButton type="button" CssClass="e-small e-success add-button" OnClick="ShowAddOnAirFormatDialog">Add New</SfButton>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Total Episodes</label>
                            <SfNumericTextBox @bind-value="CurrentProgram.TotalEpisodes" Placeholder="Enter total episodes" FloatLabelType="FloatLabelType.Never" Min="0" Format="n0"></SfNumericTextBox>
                            <small class="form-text">Enter 0 if unknown.</small>
                        </div>
                        <div class="form-group">
                            <label>Duration (minutes)</label>
                            <SfNumericTextBox @bind-value="CurrentProgram.Duration" Placeholder="Enter duration in minutes" FloatLabelType="FloatLabelType.Never" Min="0" Format="n0"></SfNumericTextBox>
                        </div>
                        <div class="form-group">
                            <label>Year of Launch</label>
                            <SfTextBox @bind-value="CurrentProgram.YearOfLaunch" Placeholder="e.g., 2024" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        </div>
                        <div class="form-group">
                            <label>Drive Serial Number</label>
                            <SfTextBox @bind-value="CurrentProgram.DriveSerialNumber" Placeholder="Enter drive serial number" FloatLabelType="FloatLabelType.Never"></SfTextBox>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h5 class="section-title">Additional Information</h5>
                    <div class="form-grid">
                        <div class="form-group full-width">
                            <label>Remarks</label>
                            <SfTextBox @bind-value="CurrentProgram.Remarks" Placeholder="Enter any additional remarks or notes" FloatLabelType="FloatLabelType.Never" Multiline="true" Rows="4"></SfTextBox>
                        </div>
                        <div class="form-group full-width">
                            <label>Syndication Delivery Remarks</label>
                            <SfTextBox @bind-value="CurrentProgram.SyndicationDeliveryRemarks" Placeholder="Enter syndication delivery remarks" FloatLabelType="FloatLabelType.Never" Multiline="true" Rows="4"></SfTextBox>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">@(IsEditMode ? "Update Program" : "Save Program")</SfButton>
                    <SfButton OnClick="CancelForm" CssClass="e-secondary">Cancel</SfButton>
                </div>
            </EditForm>
        </div>
    </div>
</div>

<!-- Inline Creation Components -->
<InlineGenreCreation @ref="GenreCreationComponent" OnGenreCreated="OnGenreCreated"/>
<InlineProductionHouseCreation @ref="ProductionHouseCreationComponent" OnProductionHouseCreated="OnProductionHouseCreated"/>
<InlineOnAirFormatCreation @ref="OnAirFormatCreationComponent" OnOnAirFormatCreated="OnOnAirFormatCreated"/>

<style>
    .program-form-container {
        /*max-width: 900px;*/
        margin: 2rem auto;
        padding: 1rem;
    }

    .card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    }

    .card-header {
        background-color: #f5f5f5;
        padding: 1rem 1.5rem;
        font-size: 1.25rem;
        font-weight: 500;
        border-bottom: 1px solid #e0e0e0;
    }

    .card-body {
        padding: 1.5rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    @@media (min-width: 768px) {
        .form-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .form-group label.required::after {
        content: " *";
        color: #dc3545;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e0e0e0;
    }

    .validation-summary {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 4px;
        margin-bottom: 1.5rem;
    }
    
    .dropdown-with-add {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .dropdown-with-add .e-combobox {
        flex-grow: 1;
    }

    .add-button {
        flex-shrink: 0;
    }
</style>

@code {
    [Parameter] public int? ProgramId { get; set; }

    private ProgramDto CurrentProgram = new();
    private List<GenreDto> Genres = new();
    private List<ProductionHouseDto> ProductionHouses = new();
    private List<OnAirFormatDto> OnAirFormats = new();
    private bool IsEditMode => ProgramId is > 0;

    private InlineGenreCreation? GenreCreationComponent;
    private InlineProductionHouseCreation? ProductionHouseCreationComponent;
    private InlineOnAirFormatCreation? OnAirFormatCreationComponent;

    protected override async Task OnInitializedAsync()
    {
        // Load genres, production houses, and on air formats
        var genresTask = GenreService.GetGenreDtosAsync();
        var productionHousesTask = ProductionHouseService.GetProductionHousesAsync();
        var onAirFormatsTask = OnAirFormatService.GetOnAirFormatDtosAsync();

        // Wait for all tasks to complete
        await Task.WhenAll(genresTask, productionHousesTask, onAirFormatsTask);

        // Assign results
        Genres = await genresTask;
        ProductionHouses = await productionHousesTask;
        OnAirFormats = await onAirFormatsTask;

        if (IsEditMode)
        {
            var program = await ProgramService.GetProgramByIdAsync(ProgramId!.Value);
            if (program != null)
            {
                CurrentProgram = program;
            }
            else
            {
                Navigation.NavigateTo("/programs");
            }
        }
        else
        {
            CurrentProgram = new ProgramDto { IsActive = true };
        }
    }

    private async Task SaveProgram()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(CurrentProgram.PrimaryName))
            {
                return;
            }

            var isUnique = await ProgramService.IsProgramNameUniqueAsync(
                CurrentProgram.PrimaryName,
                IsEditMode ? CurrentProgram.ProgramId : null);

            if (!isUnique)
            {
                // In a real app, show a user-friendly message (e.g., toast notification)
                Console.WriteLine("Program name already exists.");
                return;
            }

            if (IsEditMode)
            {
                await ProgramService.UpdateProgramAsync(CurrentProgram);
            }
            else
            {
                await ProgramService.CreateProgramAsync(CurrentProgram);
            }

            Navigation.NavigateTo("/programs");
        }
        catch (Exception ex)
        {
            // In a real app, log this error and show a user-friendly message
            Console.WriteLine($"Error saving program: {ex.Message}");
        }
    }

    private void CancelForm()
    {
        Navigation.NavigateTo("/programs");
    }

    private async Task ShowAddGenreDialog()
    {
        if (GenreCreationComponent != null)
        {
            await GenreCreationComponent.ShowDialog();
        }
    }

    private async Task ShowAddProductionHouseDialog()
    {
        if (ProductionHouseCreationComponent != null)
        {
            await ProductionHouseCreationComponent.ShowDialog();
        }
    }

    private async Task ShowAddOnAirFormatDialog()
    {
        if (OnAirFormatCreationComponent != null)
        {
            await OnAirFormatCreationComponent.ShowDialog();
        }
    }

    private void OnGenreCreated(GenreDto newGenre)
    {
        Genres.Add(newGenre);
        CurrentProgram.GenreId = newGenre.GenreId;
        StateHasChanged();
    }

    private void OnProductionHouseCreated(ProductionHouseDto newProductionHouse)
    {
        ProductionHouses.Add(newProductionHouse);
        CurrentProgram.ProductionHouseId = newProductionHouse.ProductionHouseId;
        StateHasChanged();
    }

    private void OnOnAirFormatCreated(OnAirFormatDto newOnAirFormat)
    {
        OnAirFormats.Add(newOnAirFormat);
        CurrentProgram.OnAirFormatId = newOnAirFormat.OnAirFormatId;
        StateHasChanged();
    }
}