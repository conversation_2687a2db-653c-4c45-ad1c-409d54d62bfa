@page "/admin/menus"
@using GELProgramsInventory.Models.Dtos
@using GELProgramsInventory.Services
@using GELProgramsInventory.Components.Shared
@inject GELProgramsInventory.Services.MenuService MenuService
@inject GELProgramsInventory.Services.RoleService RoleService
@rendermode InteractiveServer

<Breadcrumb CurrentPage="Admin" SubPage="Menus" />

<div style="display: flex; justify-content: space-between; align-items: flex-end;" class="mb-2 me-2">
    <div class="">
        <h1 class="mb-0">Menus Management</h1>
        <p class="page-description">Manage application menus and their module organization</p>
    </div>

    <div class="">
        <SfButton OnClick="OpenCreateDialog" CssClass="e-primary e-large" aria-label="Add new menu">
            <span class="e-btn-icon e-icons e-plus"></span>
            Add New Menu
        </SfButton>
    </div>
</div>

<SfGrid @ref="_grid" DataSource="@_menusList" AllowPaging="true" AllowSorting="true" AllowGrouping="true">
    <GridGroupSettings Columns="@(new string[] { "Module" })"></GridGroupSettings>
    <GridColumns>
        <GridColumn Field="@nameof(MenuDto.MemuTitle)" HeaderText="Menu Title" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(MenuDto.MenuLink)" HeaderText="Menu Link" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(MenuDto.Module)" HeaderText="Module" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(MenuDto.SortOrder)" HeaderText="Sort Order" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(MenuDto.Icon)" HeaderText="Icon" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Assigned Roles" AutoFit="true">
            <Template Context="menuContext">
                @{
                    var menu = (MenuDto)menuContext;
                    var roleTitles = menu.AssignedRoles.Select(r => r.RoleTitle).ToList();
                }
                @string.Join(", ", roleTitles)
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Actions" Width="200">
            <Template Context="menuContext">
                <SfButton OnClick="@(() => OpenEditDialog((MenuDto)menuContext))" CssClass="e-info">Edit</SfButton>
                <SfButton OnClick="@(() => DeleteMenu(((MenuDto)menuContext).MenuId))" CssClass="e-danger">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog @ref="dlgMenu" ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="800px" Visible="false">
    <DialogTemplates>
        <Header>Menu Information</Header>
        <Content>
            <div class="card-body">
                <EditForm Model="_selectedMenu" OnValidSubmit="SaveMenu" FormName="MenuForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>Menu Title</label>
                                <SfTextBox Placeholder="Menu Title" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedMenu.MemuTitle"></SfTextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label>Menu Link</label>
                                <SfTextBox Placeholder="/path/to/page" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedMenu.MenuLink"></SfTextBox>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label>Module</label>
                                <SfDropDownList TValue="string" TItem="string" 
                                               DataSource="@_moduleOptions" 
                                               @bind-Value="_selectedMenu.Module"
                                               Placeholder="Select Module"
                                               AllowCustom="true">
                                </SfDropDownList>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label>Sort Order</label>
                                <SfNumericTextBox TValue="int" @bind-Value="_selectedMenu.SortOrder" Placeholder="Sort Order"></SfNumericTextBox>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label>Icon</label>
                                <SfTextBox Placeholder="e-icons e-home" FloatLabelType="FloatLabelType.Never" @bind-Value="_selectedMenu.Icon"></SfTextBox>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label>Assigned Roles</label>
                        <SfMultiSelect TValue="int[]" TItem="RoleDto" 
                                      DataSource="@_rolesList" 
                                      @bind-Value="_selectedRoleIds"
                                      Placeholder="Select roles to assign"
                                      Mode="VisualMode.CheckBox">
                            <MultiSelectFieldSettings Text="RoleTitle" Value="RoleId"></MultiSelectFieldSettings>
                            <MultiSelectTemplates TItem="RoleDto">
                                <ItemTemplate Context="roleItem">
                                    <div>
                                        @roleItem.RoleTitle
                                        <span class="badge @(roleItem.RoleIsActive ? "badge-success" : "badge-secondary") ms-1">
                                            @roleItem.Status
                                        </span>
                                    </div>
                                </ItemTemplate>
                            </MultiSelectTemplates>
                        </SfMultiSelect>
                    </div>

                    <div class="form-actions">
                        <SfButton Type="submit" CssClass="e-primary" IsPrimary="true">Save</SfButton>
                        <SfButton OnClick="CloseDialog" CssClass="e-secondary">Cancel</SfButton>
                    </div>
                </EditForm>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<MenuDto> _menusList = new();
    private List<RoleDto> _rolesList = new();
    private List<string> _moduleOptions = new() { "", "Reports", "Admin" };
    private SfGrid<MenuDto> _grid = new();
    private MenuDto _selectedMenu = new();
    private SfDialog? dlgMenu;
    private int[] _selectedRoleIds = Array.Empty<int>();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _menusList = await MenuService.GetMenusAsync();
        _rolesList = await RoleService.GetRolesAsync();
        
        // Add existing modules to options
        var existingModules = await MenuService.GetModulesAsync();
        foreach (var module in existingModules)
        {
            if (!_moduleOptions.Contains(module))
            {
                _moduleOptions.Add(module);
            }
        }
    }

    private async Task OpenCreateDialog()
    {
        _selectedMenu = new MenuDto() { SortOrder = 1, CreatedBy = "system", ModifiedBy = "system" };
        _selectedRoleIds = Array.Empty<int>();
        if (dlgMenu != null)
        {
            await dlgMenu.ShowAsync();
        }
    }

    private async Task OpenEditDialog(MenuDto menu)
    {
        _selectedMenu = menu;
        _selectedRoleIds = menu.AssignedRoleIds.ToArray();
        if (dlgMenu != null)
        {
            await dlgMenu.ShowAsync();
        }
    }

    private async Task SaveMenu()
    {
        _selectedMenu.AssignedRoleIds = _selectedRoleIds.ToList();
        
        if (_selectedMenu.MenuId == 0)
        {
            await MenuService.CreateMenuAsync(_selectedMenu);
        }
        else
        {
            await MenuService.UpdateMenuAsync(_selectedMenu);
        }

        await LoadData();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
        await CloseDialog();
    }

    private async Task DeleteMenu(int menuId)
    {
        await MenuService.DeleteMenuAsync(menuId);
        await LoadData();
        if (_grid != null)
        {
            await _grid.Refresh();
        }
    }

    private async Task CloseDialog()
    {
        if (dlgMenu != null)
        {
            await dlgMenu.HideAsync();
        }
    }
}
